"""
应用程序常量定义

包含应用程序中使用的各种常量和枚举值
"""

from enum import Enum
from pathlib import Path

# 应用程序信息
APP_NAME = "普通话测试软件"
APP_VERSION = "1.0.0"
APP_AUTHOR = "开发团队"

# 文件路径
PROJECT_ROOT = Path(__file__).parent.parent
CONFIG_FILE = PROJECT_ROOT / "config.json"
RESOURCES_DIR = PROJECT_ROOT / "resources"
ICONS_DIR = RESOURCES_DIR / "icons"
STYLES_DIR = RESOURCES_DIR / "styles"
SAMPLES_DIR = RESOURCES_DIR / "samples"

# 音频相关常量
SUPPORTED_AUDIO_FORMATS = [".wav", ".mp3", ".m4a", ".pcm"]
MAX_AUDIO_FILE_SIZE_MB = 5
TARGET_SAMPLE_RATE = 16000
TARGET_CHANNELS = 1
TARGET_BIT_DEPTH = 16

# 文本相关常量
MAX_CHINESE_TEXT_BYTES = 180
MAX_ENGLISH_TEXT_BYTES = 300

# API相关常量
API_TIMEOUT = 30
API_RETRY_COUNT = 3

# 配置相关常量
CONFIG_SECTIONS = ["app", "api", "audio", "ui", "evaluation", "file", "annotation"]
SENSITIVE_CONFIG_KEYS = ["api.api_key"]
DEFAULT_WORK_DIR = "普通话测试工作目录"


class TestType(Enum):
    """测试类型枚举"""
    READ_SYLLABLE = "read_syllable"  # 读字
    READ_WORD = "read_word"          # 读词语
    READ_SENTENCE = "read_sentence"  # 读文章
    READ_CHAPTER = "read_chapter"    # 自由说话


class Language(Enum):
    """语言类型枚举"""
    CHINESE = "zh_cn"
    ENGLISH = "en_us"


class EvaluationMode(Enum):
    """评测模式枚举"""
    STANDARD = "standard"
    DETAILED = "detailed"


class Strictness(Enum):
    """评分严格程度枚举"""
    LOOSE = "loose"
    STANDARD = "standard"
    STRICT = "strict"


class Theme(Enum):
    """界面主题枚举"""
    LIGHT = "light"
    DARK = "dark"


class FontSize(Enum):
    """字体大小枚举"""
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"


class AudioQuality(Enum):
    """音频质量枚举"""
    STANDARD = "standard"
    HIGH = "high"


class ExportFormat(Enum):
    """导出格式枚举"""
    JSON = "json"
    XML = "xml"
    CSV = "csv"


class ErrorType(Enum):
    """错误类型枚举"""
    INITIAL_CONSONANT = "initial_consonant"  # 声母错误
    FINAL_SOUND = "final_sound"              # 韵母错误
    TONE = "tone"                            # 声调错误
    FLUENCY = "fluency"                      # 流畅性问题
    MISSING = "missing"                      # 漏读


class AnnotationStyle(Enum):
    """标注样式枚举"""
    INITIAL_CONSONANT = {
        "color": "red",
        "decoration": "underline",
        "marker": "声"
    }
    FINAL_SOUND = {
        "color": "blue", 
        "decoration": "underline",
        "marker": "韵"
    }
    TONE = {
        "color": "orange",
        "decoration": "wavy-underline", 
        "marker": "调"
    }
    FLUENCY = {
        "color": "yellow",
        "decoration": "highlight",
        "marker": "流"
    }
    MISSING = {
        "color": "gray",
        "decoration": "background",
        "marker": "漏"
    }


# UI相关常量
DEFAULT_WINDOW_WIDTH = 1200
DEFAULT_WINDOW_HEIGHT = 800
MIN_WINDOW_WIDTH = 800
MIN_WINDOW_HEIGHT = 600

# 字体大小
FONT_SIZES = {
    "small": 10,
    "medium": 12,
    "large": 14
}

# 主题
THEMES = ["light", "dark"]

# 状态消息
STATUS_MESSAGES = {
    "ready": "就绪",
    "uploading": "正在上传文件...",
    "processing": "正在处理音频...",
    "evaluating": "正在进行语音评测...",
    "parsing": "正在解析结果...",
    "completed": "评测完成",
    "error": "发生错误"
}
