"""
工具函数模块

包含配置管理、文件操作、验证工具等辅助功能
"""

# 版本信息
__version__ = "1.0.0"
__author__ = "开发团队"

# 导出主要的工具组件
from .config_manager import ConfigManager
from .exception_handler import setup_exception_handler, get_exception_handler
from .exception_handler import log_info, log_warning, log_error, log_debug
from .file_utils import FileUtils, file_history, ensure_directory, get_work_directory, format_file_size
from .validators import FileValidator, AudioValidator, validate_audio_file, validate_text_file, validate_text_content
from .crypto_utils import CryptoUtils, XunfeiSigner, RequestSigner, TokenManager
from .crypto_utils import md5_hash, base64_encode, create_xunfei_signer
from .text_utils import TextProcessor, PositionMapper, CharacterInfo
from .text_utils import byte_to_char_pos, char_to_byte_pos, extract_text, is_chinese
from .style_utils import StyleManager, ColorUtils, FontManager, IconManager, StyleSheets
from .style_utils import get_error_color, get_score_color, create_error_format

__all__ = [
    "ConfigManager",
    "setup_exception_handler",
    "get_exception_handler",
    "log_info",
    "log_warning",
    "log_error",
    "log_debug",
    "FileUtils",
    "file_history",
    "ensure_directory",
    "get_work_directory",
    "format_file_size",
    "FileValidator",
    "AudioValidator",
    "validate_audio_file",
    "validate_text_file",
    "validate_text_content",
    "CryptoUtils",
    "XunfeiSigner",
    "RequestSigner",
    "TokenManager",
    "md5_hash",
    "base64_encode",
    "create_xunfei_signer",
    "TextProcessor",
    "PositionMapper",
    "CharacterInfo",
    "byte_to_char_pos",
    "char_to_byte_pos",
    "extract_text",
    "is_chinese",
    "StyleManager",
    "ColorUtils",
    "FontManager",
    "IconManager",
    "StyleSheets",
    "get_error_color",
    "get_score_color",
    "create_error_format",
]
