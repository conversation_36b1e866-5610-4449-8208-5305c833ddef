"""
文件操作工具模块

提供文件处理、路径管理、临时文件等功能
"""

import os
import shutil
import tempfile
import hashlib
from pathlib import Path
from typing import List, Optional, Dict, Any, Union
from datetime import datetime

from .constants import PROJECT_ROOT, DEFAULT_WORK_DIR


class FileUtils:
    """文件操作工具类"""
    
    @staticmethod
    def ensure_directory(directory: Union[str, Path]) -> Path:
        """确保目录存在
        
        Args:
            directory: 目录路径
            
        Returns:
            目录路径对象
        """
        dir_path = Path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        return dir_path
    
    @staticmethod
    def get_work_directory() -> Path:
        """获取工作目录
        
        Returns:
            工作目录路径
        """
        # 优先使用用户文档目录
        try:
            documents_dir = Path.home() / "Documents"
            if documents_dir.exists():
                work_dir = documents_dir / DEFAULT_WORK_DIR
            else:
                work_dir = Path.home() / DEFAULT_WORK_DIR
        except Exception:
            # 备用方案：使用项目目录
            work_dir = PROJECT_ROOT / "work"
        
        return FileUtils.ensure_directory(work_dir)
    
    @staticmethod
    def get_temp_directory() -> Path:
        """获取临时目录
        
        Returns:
            临时目录路径
        """
        temp_dir = Path(tempfile.gettempdir()) / "putonghua_test"
        return FileUtils.ensure_directory(temp_dir)
    
    @staticmethod
    def generate_unique_filename(original_name: str, directory: Path) -> str:
        """生成唯一文件名
        
        Args:
            original_name: 原始文件名
            directory: 目标目录
            
        Returns:
            唯一文件名
        """
        base_path = Path(original_name)
        name_stem = base_path.stem
        suffix = base_path.suffix
        
        counter = 1
        new_name = original_name
        
        while (directory / new_name).exists():
            new_name = f"{name_stem}_{counter}{suffix}"
            counter += 1
        
        return new_name
    
    @staticmethod
    def copy_file_to_work_dir(source_path: Path, subfolder: str = "") -> Path:
        """复制文件到工作目录
        
        Args:
            source_path: 源文件路径
            subfolder: 子文件夹名称
            
        Returns:
            目标文件路径
        """
        work_dir = FileUtils.get_work_directory()
        if subfolder:
            target_dir = FileUtils.ensure_directory(work_dir / subfolder)
        else:
            target_dir = work_dir
        
        # 生成唯一文件名
        unique_name = FileUtils.generate_unique_filename(source_path.name, target_dir)
        target_path = target_dir / unique_name
        
        # 复制文件
        shutil.copy2(source_path, target_path)
        
        return target_path
    
    @staticmethod
    def create_temp_file(suffix: str = "", prefix: str = "temp_") -> Path:
        """创建临时文件
        
        Args:
            suffix: 文件后缀
            prefix: 文件前缀
            
        Returns:
            临时文件路径
        """
        temp_dir = FileUtils.get_temp_directory()
        
        # 生成唯一文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        temp_name = f"{prefix}{timestamp}{suffix}"
        
        counter = 1
        while (temp_dir / temp_name).exists():
            temp_name = f"{prefix}{timestamp}_{counter}{suffix}"
            counter += 1
        
        temp_path = temp_dir / temp_name
        temp_path.touch()  # 创建空文件
        
        return temp_path
    
    @staticmethod
    def calculate_file_hash(file_path: Path, algorithm: str = "md5") -> str:
        """计算文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法 ("md5", "sha1", "sha256")
            
        Returns:
            文件哈希值
        """
        hash_obj = hashlib.new(algorithm)
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """格式化文件大小
        
        Args:
            size_bytes: 文件大小（字节）
            
        Returns:
            格式化的文件大小字符串
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    @staticmethod
    def clean_temp_files(max_age_hours: int = 24) -> int:
        """清理临时文件
        
        Args:
            max_age_hours: 最大保留时间（小时）
            
        Returns:
            清理的文件数量
        """
        temp_dir = FileUtils.get_temp_directory()
        current_time = datetime.now().timestamp()
        max_age_seconds = max_age_hours * 3600
        
        cleaned_count = 0
        
        try:
            for file_path in temp_dir.iterdir():
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        file_path.unlink()
                        cleaned_count += 1
        except Exception as e:
            print(f"清理临时文件时发生错误: {e}")
        
        return cleaned_count
    
    @staticmethod
    def get_file_list(directory: Path, pattern: str = "*", recursive: bool = False) -> List[Path]:
        """获取目录中的文件列表
        
        Args:
            directory: 目录路径
            pattern: 文件模式
            recursive: 是否递归搜索
            
        Returns:
            文件路径列表
        """
        if not directory.exists():
            return []
        
        if recursive:
            return list(directory.rglob(pattern))
        else:
            return list(directory.glob(pattern))
    
    @staticmethod
    def backup_file(file_path: Path, backup_dir: Optional[Path] = None) -> Path:
        """备份文件
        
        Args:
            file_path: 要备份的文件路径
            backup_dir: 备份目录，None则使用默认备份目录
            
        Returns:
            备份文件路径
        """
        if backup_dir is None:
            backup_dir = FileUtils.get_work_directory() / "backups"
        
        backup_dir = FileUtils.ensure_directory(backup_dir)
        
        # 生成备份文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
        backup_path = backup_dir / backup_name
        
        # 复制文件
        shutil.copy2(file_path, backup_path)
        
        return backup_path


class FileHistory:
    """文件历史记录管理"""
    
    def __init__(self, max_records: int = 100):
        self.max_records = max_records
        self.history_file = FileUtils.get_work_directory() / "file_history.json"
        self.records = self._load_history()
    
    def _load_history(self) -> List[Dict[str, Any]]:
        """加载历史记录"""
        try:
            if self.history_file.exists():
                import json
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载文件历史记录失败: {e}")
        
        return []
    
    def _save_history(self):
        """保存历史记录"""
        try:
            import json
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.records, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存文件历史记录失败: {e}")
    
    def add_record(self, file_path: Path, operation: str, details: Dict[str, Any] = None):
        """添加历史记录
        
        Args:
            file_path: 文件路径
            operation: 操作类型
            details: 操作详情
        """
        record = {
            "timestamp": datetime.now().isoformat(),
            "file_path": str(file_path),
            "file_name": file_path.name,
            "operation": operation,
            "details": details or {}
        }
        
        self.records.insert(0, record)  # 最新记录在前
        
        # 限制记录数量
        if len(self.records) > self.max_records:
            self.records = self.records[:self.max_records]
        
        self._save_history()
    
    def get_recent_files(self, count: int = 10) -> List[Dict[str, Any]]:
        """获取最近的文件记录
        
        Args:
            count: 返回记录数量
            
        Returns:
            最近的文件记录列表
        """
        return self.records[:count]
    
    def clear_history(self):
        """清空历史记录"""
        self.records = []
        self._save_history()


# 全局文件历史记录实例
file_history = FileHistory()


# 便捷函数
def ensure_directory(directory: Union[str, Path]) -> Path:
    """确保目录存在的便捷函数"""
    return FileUtils.ensure_directory(directory)


def get_work_directory() -> Path:
    """获取工作目录的便捷函数"""
    return FileUtils.get_work_directory()


def format_file_size(size_bytes: int) -> str:
    """格式化文件大小的便捷函数"""
    return FileUtils.format_file_size(size_bytes)
