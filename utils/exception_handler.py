"""
全局异常处理模块

提供应用程序的异常处理和错误报告功能
"""

import sys
import traceback
import logging
from pathlib import Path
from typing import Optional
from PyQt6.QtWidgets import QMessageBox, QApplication
from PyQt6.QtCore import QObject, pyqtSignal

from .constants import PROJECT_ROOT


class ExceptionHandler(QObject):
    """异常处理器"""
    
    # 异常信号
    exception_occurred = pyqtSignal(str, str)  # 异常类型, 异常信息
    
    def __init__(self):
        super().__init__()
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('PutonghuaTest')
        logger.setLevel(logging.DEBUG)
        
        # 创建日志目录
        log_dir = PROJECT_ROOT / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # 文件处理器
        log_file = log_dir / "app.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        if not logger.handlers:
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)
        
        return logger
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """处理未捕获的异常"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许Ctrl+C退出
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # 格式化异常信息
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        
        # 记录到日志
        self.logger.error(f"未捕获的异常: {error_msg}")
        
        # 发送信号
        self.exception_occurred.emit(exc_type.__name__, str(exc_value))
        
        # 显示错误对话框
        self._show_error_dialog(exc_type.__name__, str(exc_value), error_msg)
    
    def _show_error_dialog(self, exc_type: str, exc_message: str, full_traceback: str):
        """显示错误对话框"""
        try:
            app = QApplication.instance()
            if app is None:
                return
            
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Icon.Critical)
            msg_box.setWindowTitle("应用程序错误")
            msg_box.setText(f"发生了一个未预期的错误：\n\n{exc_type}: {exc_message}")
            msg_box.setDetailedText(full_traceback)
            msg_box.setStandardButtons(
                QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Close
            )
            msg_box.setDefaultButton(QMessageBox.StandardButton.Ok)
            
            result = msg_box.exec()
            if result == QMessageBox.StandardButton.Close:
                app.quit()
                
        except Exception as e:
            # 如果连错误对话框都无法显示，则直接打印
            print(f"无法显示错误对话框: {e}")
            print(f"原始错误: {exc_type}: {exc_message}")
    
    def log_info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)
    
    def log_warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(message)
    
    def log_error(self, message: str):
        """记录错误日志"""
        self.logger.error(message)
    
    def log_debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(message)


# 全局异常处理器实例
_exception_handler: Optional[ExceptionHandler] = None


def setup_exception_handler() -> ExceptionHandler:
    """设置全局异常处理器"""
    global _exception_handler
    
    if _exception_handler is None:
        _exception_handler = ExceptionHandler()
        sys.excepthook = _exception_handler.handle_exception
    
    return _exception_handler


def get_exception_handler() -> Optional[ExceptionHandler]:
    """获取全局异常处理器"""
    return _exception_handler


def log_info(message: str):
    """记录信息日志"""
    if _exception_handler:
        _exception_handler.log_info(message)


def log_warning(message: str):
    """记录警告日志"""
    if _exception_handler:
        _exception_handler.log_warning(message)


def log_error(message: str):
    """记录错误日志"""
    if _exception_handler:
        _exception_handler.log_error(message)


def log_debug(message: str):
    """记录调试日志"""
    if _exception_handler:
        _exception_handler.log_debug(message)
