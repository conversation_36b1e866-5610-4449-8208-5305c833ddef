"""
文件验证工具模块

提供各种文件格式和内容的验证功能
"""

import os
import mimetypes
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any

from .constants import (
    SUPPORTED_AUDIO_FORMATS, MAX_AUDIO_FILE_SIZE_MB,
    MAX_CHINESE_TEXT_BYTES, MAX_ENGLISH_TEXT_BYTES
)


class FileValidator:
    """文件验证器
    
    提供文件格式、大小、内容等验证功能
    """
    
    @staticmethod
    def validate_audio_file(file_path: Path) -> Tuple[bool, str]:
        """验证音频文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            # 检查文件是否存在
            if not file_path.exists():
                return False, "文件不存在"
            
            # 检查文件扩展名
            file_ext = file_path.suffix.lower()
            if file_ext not in SUPPORTED_AUDIO_FORMATS:
                return False, f"不支持的音频格式：{file_ext}。支持的格式：{', '.join(SUPPORTED_AUDIO_FORMATS)}"
            
            # 检查文件大小
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
            if file_size_mb > MAX_AUDIO_FILE_SIZE_MB:
                return False, f"文件大小超过限制：{file_size_mb:.1f}MB > {MAX_AUDIO_FILE_SIZE_MB}MB"
            
            # 检查MIME类型
            mime_type, _ = mimetypes.guess_type(str(file_path))
            if mime_type and not mime_type.startswith('audio/'):
                return False, f"文件类型不是音频文件：{mime_type}"
            
            return True, ""
            
        except Exception as e:
            return False, f"验证文件时发生错误：{str(e)}"
    
    @staticmethod
    def validate_text_file(file_path: Path) -> Tuple[bool, str]:
        """验证文本文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            # 检查文件是否存在
            if not file_path.exists():
                return False, "文件不存在"
            
            # 检查文件扩展名
            file_ext = file_path.suffix.lower()
            if file_ext not in ['.txt', '.text']:
                return False, f"不支持的文本格式：{file_ext}。支持的格式：.txt, .text"
            
            # 检查文件大小（简单检查）
            file_size = file_path.stat().st_size
            if file_size > 1024 * 1024:  # 1MB
                return False, "文本文件过大（超过1MB）"
            
            # 尝试读取文件内容
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 验证内容长度
                is_valid, error = FileValidator.validate_text_content(content)
                if not is_valid:
                    return False, error
                
            except UnicodeDecodeError:
                return False, "文件编码不是UTF-8，请使用UTF-8编码保存文本文件"
            
            return True, ""
            
        except Exception as e:
            return False, f"验证文本文件时发生错误：{str(e)}"
    
    @staticmethod
    def validate_text_content(content: str, language: str = "auto") -> Tuple[bool, str]:
        """验证文本内容
        
        Args:
            content: 文本内容
            language: 语言类型 ("zh", "en", "auto")
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            if not content.strip():
                return False, "文本内容不能为空"
            
            # 自动检测语言
            if language == "auto":
                # 简单的语言检测：如果包含中文字符则认为是中文
                has_chinese = any('\u4e00' <= char <= '\u9fff' for char in content)
                language = "zh" if has_chinese else "en"
            
            # 检查字节长度
            content_bytes = content.encode('utf-8')
            
            if language == "zh":
                if len(content_bytes) > MAX_CHINESE_TEXT_BYTES:
                    return False, f"中文文本过长：{len(content_bytes)}字节 > {MAX_CHINESE_TEXT_BYTES}字节"
            else:
                if len(content_bytes) > MAX_ENGLISH_TEXT_BYTES:
                    return False, f"英文文本过长：{len(content_bytes)}字节 > {MAX_ENGLISH_TEXT_BYTES}字节"
            
            return True, ""
            
        except Exception as e:
            return False, f"验证文本内容时发生错误：{str(e)}"
    
    @staticmethod
    def get_file_info(file_path: Path) -> Dict[str, Any]:
        """获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        try:
            stat = file_path.stat()
            mime_type, _ = mimetypes.guess_type(str(file_path))
            
            return {
                "name": file_path.name,
                "path": str(file_path),
                "size": stat.st_size,
                "size_mb": stat.st_size / (1024 * 1024),
                "extension": file_path.suffix.lower(),
                "mime_type": mime_type,
                "modified_time": stat.st_mtime,
                "is_audio": mime_type and mime_type.startswith('audio/') if mime_type else False,
                "is_text": file_path.suffix.lower() in ['.txt', '.text']
            }
            
        except Exception as e:
            return {
                "name": file_path.name,
                "path": str(file_path),
                "error": str(e)
            }
    
    @staticmethod
    def validate_batch_files(file_paths: List[Path]) -> Dict[str, List[Dict]]:
        """批量验证文件
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            验证结果字典 {"valid": [...], "invalid": [...]}
        """
        valid_files = []
        invalid_files = []
        
        for file_path in file_paths:
            file_info = FileValidator.get_file_info(file_path)
            
            # 根据文件类型进行验证
            if file_info.get("is_audio"):
                is_valid, error = FileValidator.validate_audio_file(file_path)
            elif file_info.get("is_text"):
                is_valid, error = FileValidator.validate_text_file(file_path)
            else:
                is_valid, error = False, "不支持的文件类型"
            
            file_info["validation_error"] = error
            
            if is_valid:
                valid_files.append(file_info)
            else:
                invalid_files.append(file_info)
        
        return {
            "valid": valid_files,
            "invalid": invalid_files
        }


class AudioValidator:
    """音频文件专用验证器"""
    
    @staticmethod
    def validate_audio_properties(file_path: Path) -> Tuple[bool, str, Dict[str, Any]]:
        """验证音频文件属性
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            (是否有效, 错误信息, 音频属性)
        """
        try:
            from pydub import AudioSegment
            
            # 加载音频文件
            audio = AudioSegment.from_file(str(file_path))
            
            properties = {
                "duration_seconds": len(audio) / 1000.0,
                "sample_rate": audio.frame_rate,
                "channels": audio.channels,
                "sample_width": audio.sample_width,
                "frame_count": audio.frame_count(),
                "max_possible_amplitude": audio.max_possible_amplitude
            }
            
            # 验证音频属性
            if properties["duration_seconds"] < 0.5:
                return False, "音频时长过短（少于0.5秒）", properties
            
            if properties["duration_seconds"] > 120:  # 2分钟
                return False, "音频时长过长（超过2分钟）", properties
            
            if properties["sample_rate"] < 8000:
                return False, f"采样率过低：{properties['sample_rate']}Hz < 8000Hz", properties
            
            if properties["channels"] > 2:
                return False, f"声道数过多：{properties['channels']} > 2", properties
            
            return True, "", properties
            
        except ImportError:
            return False, "缺少pydub库，无法验证音频属性", {}
        except Exception as e:
            return False, f"验证音频属性时发生错误：{str(e)}", {}
    
    @staticmethod
    def check_audio_quality(file_path: Path) -> Dict[str, Any]:
        """检查音频质量
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            音频质量信息
        """
        try:
            from pydub import AudioSegment
            import numpy as np
            
            audio = AudioSegment.from_file(str(file_path))
            
            # 转换为numpy数组进行分析
            samples = np.array(audio.get_array_of_samples())
            if audio.channels == 2:
                samples = samples.reshape((-1, 2))
                samples = samples.mean(axis=1)  # 转为单声道
            
            # 计算音频质量指标
            rms = np.sqrt(np.mean(samples**2))
            peak = np.max(np.abs(samples))
            
            quality_info = {
                "rms_level": float(rms),
                "peak_level": float(peak),
                "dynamic_range": float(peak - rms) if peak > rms else 0,
                "is_silent": rms < (audio.max_possible_amplitude * 0.01),
                "is_clipped": peak >= audio.max_possible_amplitude * 0.95,
                "quality_score": 0  # 将在下面计算
            }
            
            # 计算质量评分 (0-100)
            score = 100
            if quality_info["is_silent"]:
                score -= 50
            if quality_info["is_clipped"]:
                score -= 30
            if quality_info["dynamic_range"] < audio.max_possible_amplitude * 0.1:
                score -= 20
            
            quality_info["quality_score"] = max(0, score)
            
            return quality_info
            
        except ImportError:
            return {"error": "缺少pydub或numpy库"}
        except Exception as e:
            return {"error": f"检查音频质量时发生错误：{str(e)}"}


# 便捷函数
def validate_audio_file(file_path: str) -> Tuple[bool, str]:
    """验证音频文件的便捷函数"""
    return FileValidator.validate_audio_file(Path(file_path))


def validate_text_file(file_path: str) -> Tuple[bool, str]:
    """验证文本文件的便捷函数"""
    return FileValidator.validate_text_file(Path(file_path))


def validate_text_content(content: str, language: str = "auto") -> Tuple[bool, str]:
    """验证文本内容的便捷函数"""
    return FileValidator.validate_text_content(content, language)
