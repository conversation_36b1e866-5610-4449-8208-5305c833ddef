"""
样式工具模块

提供UI样式、颜色、字体等工具函数
"""

from typing import Dict, Any, Optional, Tuple
from PyQt6.QtGui import QColor, QFont, QPalette, QTextCharFormat, QBrush
from PyQt6.QtCore import Qt

from utils.constants import ErrorType, AnnotationStyle


class StyleManager:
    """样式管理器
    
    管理应用程序的颜色、字体、样式等
    """
    
    # 错误类型颜色映射
    ERROR_COLORS = {
        ErrorType.INITIAL_CONSONANT: "#FF6B6B",  # 红色 - 声母错误
        ErrorType.FINAL_SOUND: "#4ECDC4",       # 青色 - 韵母错误
        ErrorType.TONE: "#FFE66D",              # 黄色 - 声调错误
        ErrorType.FLUENCY: "#FF8B94",           # 粉色 - 流畅性错误
        ErrorType.MISSING: "#A8E6CF"            # 绿色 - 漏读错误
    }
    
    # 评分等级颜色
    SCORE_COLORS = {
        "excellent": "#4CAF50",  # 绿色 - 优秀
        "good": "#8BC34A",       # 浅绿 - 良好
        "fair": "#FFC107",       # 黄色 - 及格
        "poor": "#F44336"        # 红色 - 不及格
    }
    
    # 主题颜色
    THEME_COLORS = {
        "primary": "#2196F3",
        "secondary": "#FFC107",
        "success": "#4CAF50",
        "warning": "#FF9800",
        "error": "#F44336",
        "info": "#00BCD4"
    }
    
    @staticmethod
    def get_error_color(error_type: ErrorType) -> str:
        """获取错误类型对应的颜色
        
        Args:
            error_type: 错误类型
            
        Returns:
            颜色代码
        """
        return StyleManager.ERROR_COLORS.get(error_type, "#808080")
    
    @staticmethod
    def get_score_color(score: float) -> str:
        """根据分数获取颜色
        
        Args:
            score: 分数
            
        Returns:
            颜色代码
        """
        if score >= 90:
            return StyleManager.SCORE_COLORS["excellent"]
        elif score >= 80:
            return StyleManager.SCORE_COLORS["good"]
        elif score >= 70:
            return StyleManager.SCORE_COLORS["fair"]
        else:
            return StyleManager.SCORE_COLORS["poor"]
    
    @staticmethod
    def create_text_format(color: str, bold: bool = False, 
                          underline: bool = False, background: str = None) -> QTextCharFormat:
        """创建文本格式
        
        Args:
            color: 文字颜色
            bold: 是否粗体
            underline: 是否下划线
            background: 背景颜色
            
        Returns:
            文本格式对象
        """
        format = QTextCharFormat()
        
        # 设置文字颜色
        format.setForeground(QBrush(QColor(color)))
        
        # 设置粗体
        if bold:
            format.setFontWeight(QFont.Weight.Bold)
        
        # 设置下划线
        if underline:
            format.setUnderlineStyle(QTextCharFormat.UnderlineStyle.SingleUnderline)
        
        # 设置背景颜色
        if background:
            format.setBackground(QBrush(QColor(background)))
        
        return format
    
    @staticmethod
    def get_error_text_format(error_type: ErrorType) -> QTextCharFormat:
        """获取错误类型对应的文本格式
        
        Args:
            error_type: 错误类型
            
        Returns:
            文本格式对象
        """
        color = StyleManager.get_error_color(error_type)
        
        # 根据错误类型设置不同的样式
        if error_type == ErrorType.INITIAL_CONSONANT:
            return StyleManager.create_text_format(color, bold=True, underline=True)
        elif error_type == ErrorType.FINAL_SOUND:
            return StyleManager.create_text_format(color, bold=True)
        elif error_type == ErrorType.TONE:
            return StyleManager.create_text_format(color, underline=True)
        elif error_type == ErrorType.FLUENCY:
            return StyleManager.create_text_format(color, background="#FFF3E0")
        elif error_type == ErrorType.MISSING:
            return StyleManager.create_text_format("#666666", background=color)
        else:
            return StyleManager.create_text_format(color)


class ColorUtils:
    """颜色工具类"""
    
    @staticmethod
    def hex_to_rgb(hex_color: str) -> Tuple[int, int, int]:
        """十六进制颜色转RGB
        
        Args:
            hex_color: 十六进制颜色代码
            
        Returns:
            RGB元组
        """
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    @staticmethod
    def rgb_to_hex(r: int, g: int, b: int) -> str:
        """RGB转十六进制颜色
        
        Args:
            r: 红色分量
            g: 绿色分量
            b: 蓝色分量
            
        Returns:
            十六进制颜色代码
        """
        return f"#{r:02x}{g:02x}{b:02x}"
    
    @staticmethod
    def lighten_color(hex_color: str, factor: float = 0.2) -> str:
        """使颜色变浅
        
        Args:
            hex_color: 原始颜色
            factor: 变浅因子 (0-1)
            
        Returns:
            变浅后的颜色
        """
        r, g, b = ColorUtils.hex_to_rgb(hex_color)
        
        r = min(255, int(r + (255 - r) * factor))
        g = min(255, int(g + (255 - g) * factor))
        b = min(255, int(b + (255 - b) * factor))
        
        return ColorUtils.rgb_to_hex(r, g, b)
    
    @staticmethod
    def darken_color(hex_color: str, factor: float = 0.2) -> str:
        """使颜色变深
        
        Args:
            hex_color: 原始颜色
            factor: 变深因子 (0-1)
            
        Returns:
            变深后的颜色
        """
        r, g, b = ColorUtils.hex_to_rgb(hex_color)
        
        r = max(0, int(r * (1 - factor)))
        g = max(0, int(g * (1 - factor)))
        b = max(0, int(b * (1 - factor)))
        
        return ColorUtils.rgb_to_hex(r, g, b)
    
    @staticmethod
    def get_contrast_color(hex_color: str) -> str:
        """获取对比色（黑色或白色）
        
        Args:
            hex_color: 背景颜色
            
        Returns:
            对比色（#000000 或 #FFFFFF）
        """
        r, g, b = ColorUtils.hex_to_rgb(hex_color)
        
        # 计算亮度
        luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255
        
        return "#000000" if luminance > 0.5 else "#FFFFFF"


class FontManager:
    """字体管理器"""
    
    # 默认字体配置
    DEFAULT_FONTS = {
        "default": ("Microsoft YaHei", 12),
        "title": ("Microsoft YaHei", 16),
        "subtitle": ("Microsoft YaHei", 14),
        "content": ("Microsoft YaHei", 12),
        "small": ("Microsoft YaHei", 10),
        "code": ("Consolas", 11)
    }
    
    @staticmethod
    def get_font(font_type: str = "default", size: Optional[int] = None) -> QFont:
        """获取字体
        
        Args:
            font_type: 字体类型
            size: 字体大小（可选）
            
        Returns:
            字体对象
        """
        family, default_size = FontManager.DEFAULT_FONTS.get(font_type, FontManager.DEFAULT_FONTS["default"])
        font_size = size or default_size
        
        font = QFont(family, font_size)
        return font
    
    @staticmethod
    def get_bold_font(font_type: str = "default", size: Optional[int] = None) -> QFont:
        """获取粗体字体
        
        Args:
            font_type: 字体类型
            size: 字体大小（可选）
            
        Returns:
            粗体字体对象
        """
        font = FontManager.get_font(font_type, size)
        font.setBold(True)
        return font


class IconManager:
    """图标管理器"""
    
    # 错误类型图标映射
    ERROR_ICONS = {
        ErrorType.INITIAL_CONSONANT: "🔴",  # 红圆 - 声母
        ErrorType.FINAL_SOUND: "🔵",       # 蓝圆 - 韵母
        ErrorType.TONE: "🟡",              # 黄圆 - 声调
        ErrorType.FLUENCY: "🟣",           # 紫圆 - 流畅性
        ErrorType.MISSING: "⚪"            # 白圆 - 漏读
    }
    
    # 评分等级图标
    SCORE_ICONS = {
        "excellent": "🌟",  # 优秀
        "good": "✅",       # 良好
        "fair": "⚠️",       # 及格
        "poor": "❌"        # 不及格
    }
    
    @staticmethod
    def get_error_icon(error_type: ErrorType) -> str:
        """获取错误类型图标
        
        Args:
            error_type: 错误类型
            
        Returns:
            图标字符
        """
        return IconManager.ERROR_ICONS.get(error_type, "⚫")
    
    @staticmethod
    def get_score_icon(score: float) -> str:
        """根据分数获取图标
        
        Args:
            score: 分数
            
        Returns:
            图标字符
        """
        if score >= 90:
            return IconManager.SCORE_ICONS["excellent"]
        elif score >= 80:
            return IconManager.SCORE_ICONS["good"]
        elif score >= 70:
            return IconManager.SCORE_ICONS["fair"]
        else:
            return IconManager.SCORE_ICONS["poor"]


class StyleSheets:
    """样式表集合"""
    
    @staticmethod
    def get_card_style() -> str:
        """获取卡片样式"""
        return """
            QWidget {
                background-color: #FFFFFF;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 16px;
            }
            QWidget:hover {
                border-color: #BDBDBD;
            }
        """
    
    @staticmethod
    def get_button_style(color: str = "#2196F3") -> str:
        """获取按钮样式
        
        Args:
            color: 按钮颜色
            
        Returns:
            样式表字符串
        """
        hover_color = ColorUtils.darken_color(color, 0.1)
        pressed_color = ColorUtils.darken_color(color, 0.2)
        
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {pressed_color};
            }}
            QPushButton:disabled {{
                background-color: #BDBDBD;
                color: #757575;
            }}
        """
    
    @staticmethod
    def get_text_edit_style() -> str:
        """获取文本编辑器样式"""
        return """
            QTextEdit {
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                padding: 8px;
                font-family: "Microsoft YaHei";
                font-size: 12px;
                line-height: 1.5;
            }
            QTextEdit:focus {
                border-color: #2196F3;
            }
        """


# 便捷函数
def get_error_color(error_type: ErrorType) -> str:
    """获取错误颜色的便捷函数"""
    return StyleManager.get_error_color(error_type)


def get_score_color(score: float) -> str:
    """获取分数颜色的便捷函数"""
    return StyleManager.get_score_color(score)


def create_error_format(error_type: ErrorType) -> QTextCharFormat:
    """创建错误格式的便捷函数"""
    return StyleManager.get_error_text_format(error_type)
