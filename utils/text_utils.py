"""
文本处理工具模块

提供文本分析、位置映射、字符处理等功能
"""

import re
import unicodedata
from typing import List, Tuple, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class CharacterInfo:
    """字符信息数据类"""
    char: str                   # 字符
    byte_start: int            # 字节开始位置
    byte_end: int              # 字节结束位置
    char_index: int            # 字符索引
    is_chinese: bool           # 是否为中文字符
    is_punctuation: bool       # 是否为标点符号
    is_whitespace: bool        # 是否为空白字符


class TextProcessor:
    """文本处理器
    
    提供文本分析和位置映射功能
    """
    
    @staticmethod
    def analyze_text(text: str) -> List[CharacterInfo]:
        """分析文本，返回字符信息列表
        
        Args:
            text: 要分析的文本
            
        Returns:
            字符信息列表
        """
        char_infos = []
        text_bytes = text.encode('utf-8')
        byte_pos = 0
        
        for char_index, char in enumerate(text):
            char_bytes = char.encode('utf-8')
            char_byte_len = len(char_bytes)
            
            char_info = CharacterInfo(
                char=char,
                byte_start=byte_pos,
                byte_end=byte_pos + char_byte_len,
                char_index=char_index,
                is_chinese=TextProcessor.is_chinese_char(char),
                is_punctuation=TextProcessor.is_punctuation(char),
                is_whitespace=char.isspace()
            )
            
            char_infos.append(char_info)
            byte_pos += char_byte_len
        
        return char_infos
    
    @staticmethod
    def byte_pos_to_char_pos(text: str, byte_start: int, byte_end: int) -> Tuple[int, int]:
        """将字节位置转换为字符位置
        
        Args:
            text: 原始文本
            byte_start: 字节开始位置
            byte_end: 字节结束位置
            
        Returns:
            (字符开始位置, 字符结束位置)
        """
        char_infos = TextProcessor.analyze_text(text)
        
        char_start = None
        char_end = None
        
        for char_info in char_infos:
            # 找到字节开始位置对应的字符位置
            if char_start is None and char_info.byte_start <= byte_start < char_info.byte_end:
                char_start = char_info.char_index
            
            # 找到字节结束位置对应的字符位置
            if char_info.byte_start < byte_end <= char_info.byte_end:
                char_end = char_info.char_index + 1
                break
        
        # 处理边界情况
        if char_start is None:
            char_start = 0
        if char_end is None:
            char_end = len(text)
        
        return char_start, char_end
    
    @staticmethod
    def char_pos_to_byte_pos(text: str, char_start: int, char_end: int) -> Tuple[int, int]:
        """将字符位置转换为字节位置
        
        Args:
            text: 原始文本
            char_start: 字符开始位置
            char_end: 字符结束位置
            
        Returns:
            (字节开始位置, 字节结束位置)
        """
        char_infos = TextProcessor.analyze_text(text)
        
        if char_start >= len(char_infos):
            return len(text.encode('utf-8')), len(text.encode('utf-8'))
        
        if char_end > len(char_infos):
            char_end = len(char_infos)
        
        byte_start = char_infos[char_start].byte_start if char_start < len(char_infos) else len(text.encode('utf-8'))
        byte_end = char_infos[char_end - 1].byte_end if char_end > 0 and char_end <= len(char_infos) else len(text.encode('utf-8'))
        
        return byte_start, byte_end
    
    @staticmethod
    def extract_text_segment(text: str, byte_start: int, byte_end: int) -> str:
        """根据字节位置提取文本片段
        
        Args:
            text: 原始文本
            byte_start: 字节开始位置
            byte_end: 字节结束位置
            
        Returns:
            提取的文本片段
        """
        char_start, char_end = TextProcessor.byte_pos_to_char_pos(text, byte_start, byte_end)
        return text[char_start:char_end]
    
    @staticmethod
    def is_chinese_char(char: str) -> bool:
        """判断是否为中文字符
        
        Args:
            char: 字符
            
        Returns:
            是否为中文字符
        """
        return '\u4e00' <= char <= '\u9fff'
    
    @staticmethod
    def is_punctuation(char: str) -> bool:
        """判断是否为标点符号
        
        Args:
            char: 字符
            
        Returns:
            是否为标点符号
        """
        return unicodedata.category(char).startswith('P')
    
    @staticmethod
    def split_into_words(text: str) -> List[Tuple[str, int, int]]:
        """将文本分割为单词/字符
        
        Args:
            text: 原始文本
            
        Returns:
            (单词, 字符开始位置, 字符结束位置) 的列表
        """
        words = []
        char_infos = TextProcessor.analyze_text(text)
        
        current_word = ""
        word_start = 0
        
        for i, char_info in enumerate(char_infos):
            if char_info.is_whitespace or char_info.is_punctuation:
                # 遇到空白或标点，结束当前单词
                if current_word:
                    words.append((current_word, word_start, i))
                    current_word = ""
                
                # 标点符号作为单独的"单词"
                if char_info.is_punctuation:
                    words.append((char_info.char, i, i + 1))
            else:
                # 开始新单词
                if not current_word:
                    word_start = i
                current_word += char_info.char
        
        # 处理最后一个单词
        if current_word:
            words.append((current_word, word_start, len(char_infos)))
        
        return words
    
    @staticmethod
    def split_into_sentences(text: str) -> List[Tuple[str, int, int]]:
        """将文本分割为句子
        
        Args:
            text: 原始文本
            
        Returns:
            (句子, 字符开始位置, 字符结束位置) 的列表
        """
        # 中文句子分隔符
        sentence_endings = r'[。！？；.!?;]'
        
        sentences = []
        last_end = 0
        
        for match in re.finditer(sentence_endings, text):
            sentence_end = match.end()
            sentence_text = text[last_end:sentence_end].strip()
            
            if sentence_text:
                sentences.append((sentence_text, last_end, sentence_end))
            
            last_end = sentence_end
        
        # 处理最后一个句子（如果没有以句号结尾）
        if last_end < len(text):
            remaining_text = text[last_end:].strip()
            if remaining_text:
                sentences.append((remaining_text, last_end, len(text)))
        
        return sentences
    
    @staticmethod
    def normalize_text(text: str) -> str:
        """标准化文本
        
        Args:
            text: 原始文本
            
        Returns:
            标准化后的文本
        """
        # Unicode标准化
        text = unicodedata.normalize('NFKC', text)
        
        # 去除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 去除首尾空白
        text = text.strip()
        
        return text
    
    @staticmethod
    def get_text_statistics(text: str) -> Dict[str, Any]:
        """获取文本统计信息
        
        Args:
            text: 文本
            
        Returns:
            统计信息字典
        """
        char_infos = TextProcessor.analyze_text(text)
        
        stats = {
            'total_chars': len(text),
            'total_bytes': len(text.encode('utf-8')),
            'chinese_chars': sum(1 for info in char_infos if info.is_chinese),
            'punctuation_chars': sum(1 for info in char_infos if info.is_punctuation),
            'whitespace_chars': sum(1 for info in char_infos if info.is_whitespace),
            'words': len(TextProcessor.split_into_words(text)),
            'sentences': len(TextProcessor.split_into_sentences(text))
        }
        
        stats['non_chinese_chars'] = stats['total_chars'] - stats['chinese_chars']
        stats['content_chars'] = stats['total_chars'] - stats['whitespace_chars']
        
        return stats


class PositionMapper:
    """位置映射器
    
    用于在不同文本表示之间进行位置映射
    """
    
    def __init__(self, original_text: str, normalized_text: str = None):
        """初始化位置映射器
        
        Args:
            original_text: 原始文本
            normalized_text: 标准化文本（可选）
        """
        self.original_text = original_text
        self.normalized_text = normalized_text or TextProcessor.normalize_text(original_text)
        self.original_char_infos = TextProcessor.analyze_text(original_text)
        self.normalized_char_infos = TextProcessor.analyze_text(self.normalized_text)
        
        # 构建映射关系
        self._build_mapping()
    
    def _build_mapping(self):
        """构建原始文本和标准化文本之间的映射关系"""
        # 这里可以实现更复杂的映射逻辑
        # 目前简化处理，假设标准化主要是去除多余空白
        pass
    
    def map_position_to_original(self, normalized_byte_start: int, normalized_byte_end: int) -> Tuple[int, int]:
        """将标准化文本的位置映射到原始文本
        
        Args:
            normalized_byte_start: 标准化文本的字节开始位置
            normalized_byte_end: 标准化文本的字节结束位置
            
        Returns:
            (原始文本字节开始位置, 原始文本字节结束位置)
        """
        # 简化实现：如果文本相同，直接返回
        if self.original_text == self.normalized_text:
            return normalized_byte_start, normalized_byte_end
        
        # 更复杂的映射逻辑可以在这里实现
        # 目前返回近似位置
        return normalized_byte_start, normalized_byte_end
    
    def map_position_to_normalized(self, original_byte_start: int, original_byte_end: int) -> Tuple[int, int]:
        """将原始文本的位置映射到标准化文本
        
        Args:
            original_byte_start: 原始文本的字节开始位置
            original_byte_end: 原始文本的字节结束位置
            
        Returns:
            (标准化文本字节开始位置, 标准化文本字节结束位置)
        """
        # 简化实现：如果文本相同，直接返回
        if self.original_text == self.normalized_text:
            return original_byte_start, original_byte_end
        
        # 更复杂的映射逻辑可以在这里实现
        return original_byte_start, original_byte_end


# 便捷函数
def byte_to_char_pos(text: str, byte_start: int, byte_end: int) -> Tuple[int, int]:
    """字节位置转字符位置的便捷函数"""
    return TextProcessor.byte_pos_to_char_pos(text, byte_start, byte_end)


def char_to_byte_pos(text: str, char_start: int, char_end: int) -> Tuple[int, int]:
    """字符位置转字节位置的便捷函数"""
    return TextProcessor.char_pos_to_byte_pos(text, char_start, char_end)


def extract_text(text: str, byte_start: int, byte_end: int) -> str:
    """提取文本片段的便捷函数"""
    return TextProcessor.extract_text_segment(text, byte_start, byte_end)


def is_chinese(char: str) -> bool:
    """判断中文字符的便捷函数"""
    return TextProcessor.is_chinese_char(char)
