"""
配置管理模块

提供应用程序配置的加载、保存、验证和管理功能
"""

import json
import os
import base64
from pathlib import Path
from typing import Dict, Any, Optional, Union
from cryptography.fernet import Fernet

from .constants import CONFIG_FILE, APP_NAME, APP_VERSION

try:
    from PyQt6.QtCore import QObject, pyqtSignal
    HAS_QT = True
except ImportError:
    # 如果没有PyQt6，创建一个简单的基类
    class QObject:
        def __init__(self):
            pass

    def pyqtSignal(*args):
        return None

    HAS_QT = False


class ConfigManager(QObject):
    """配置管理器
    
    负责管理应用程序的所有配置项，包括：
    - 应用程序设置
    - API配置（安全存储）
    - 用户界面设置
    - 音频处理设置
    - 评测参数设置
    """
    
    # 配置变更信号
    config_changed = pyqtSignal(str, object)  # 配置项名称, 新值
    
    def __init__(self, config_file: Optional[Union[Path, str]] = None):
        super().__init__()
        if config_file is None:
            self.config_file = CONFIG_FILE
        elif isinstance(config_file, str):
            self.config_file = Path(config_file)
        else:
            self.config_file = config_file
        self._config = {}
        self._default_config = self._get_default_config()
        self._encryption_key = self._get_or_create_encryption_key()
        
        # 加载配置
        self.load_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "app": {
                "name": APP_NAME,
                "version": APP_VERSION,
                "author": "开发团队",
                "description": "基于AI语音评测技术的普通话测试软件",
                "first_run": True
            },
            "api": {
                "base_url": "https://api.xfyun.cn/v1/service/v1/ise",
                "app_id": "",
                "api_key": "",
                "timeout": 30,
                "retry_count": 3
            },
            "audio": {
                "supported_formats": ["wav", "mp3", "m4a", "pcm"],
                "max_file_size_mb": 5,
                "target_sample_rate": 16000,
                "target_channels": 1,
                "target_bit_depth": 16
            },
            "ui": {
                "window": {
                    "width": 1200,
                    "height": 800,
                    "min_width": 800,
                    "min_height": 600,
                    "x": -1,  # -1表示居中
                    "y": -1,
                    "maximized": False
                },
                "theme": "light",
                "font_size": "medium",
                "language": "zh_CN",
                "show_tooltips": True,
                "auto_save": True
            },
            "evaluation": {
                "language": "zh_cn",
                "mode": "standard",
                "strictness": "standard",
                "enable_multi_dimension": True,
                "auto_start": False
            },
            "file": {
                "work_directory": "",
                "save_history": True,
                "audio_quality": "standard",
                "export_format": "json",
                "max_history_count": 100
            },
            "annotation": {
                "show_initial_consonant": True,
                "show_final_sound": True,
                "show_tone": True,
                "show_fluency": True,
                "show_missing": True,
                "animation_enabled": True
            }
        }
    
    def _get_or_create_encryption_key(self) -> bytes:
        """获取或创建加密密钥"""
        key_file = self.config_file.parent / ".key"
        
        if key_file.exists():
            try:
                with open(key_file, 'rb') as f:
                    return f.read()
            except Exception:
                pass
        
        # 创建新密钥
        key = Fernet.generate_key()
        try:
            with open(key_file, 'wb') as f:
                f.write(key)
            # 设置文件为隐藏（Windows）
            if os.name == 'nt':
                os.system(f'attrib +h "{key_file}"')
        except Exception as e:
            print(f"警告：无法保存加密密钥: {e}")
        
        return key
    
    def _encrypt_value(self, value: str) -> str:
        """加密敏感值"""
        if not value:
            return ""
        
        try:
            fernet = Fernet(self._encryption_key)
            encrypted = fernet.encrypt(value.encode())
            return base64.b64encode(encrypted).decode()
        except Exception as e:
            print(f"加密失败: {e}")
            return value
    
    def _decrypt_value(self, encrypted_value: str) -> str:
        """解密敏感值"""
        if not encrypted_value:
            return ""
        
        try:
            fernet = Fernet(self._encryption_key)
            encrypted_bytes = base64.b64decode(encrypted_value.encode())
            decrypted = fernet.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            print(f"解密失败: {e}")
            return encrypted_value
    
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                
                # 合并默认配置和加载的配置
                self._config = self._merge_config(self._default_config, loaded_config)
                
                # 解密敏感信息
                self._decrypt_sensitive_data()
                
                return True
            else:
                # 使用默认配置
                self._config = self._default_config.copy()
                self.save_config()
                return True
                
        except Exception as e:
            print(f"加载配置失败: {e}")
            self._config = self._default_config.copy()
            return False
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            # 创建配置目录
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制配置并加密敏感信息
            config_to_save = self._config.copy()
            self._encrypt_sensitive_data(config_to_save)
            
            # 保存到文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    def _merge_config(self, default: Dict, loaded: Dict) -> Dict:
        """合并配置，保留默认值的同时更新已有值"""
        result = default.copy()
        
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _encrypt_sensitive_data(self, config: Dict) -> None:
        """加密配置中的敏感数据"""
        if 'api' in config:
            if 'api_key' in config['api'] and config['api']['api_key']:
                config['api']['api_key'] = self._encrypt_value(config['api']['api_key'])
    
    def _decrypt_sensitive_data(self) -> None:
        """解密配置中的敏感数据"""
        if 'api' in self._config:
            if 'api_key' in self._config['api'] and self._config['api']['api_key']:
                self._config['api']['api_key'] = self._decrypt_value(self._config['api']['api_key'])
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键，如 'ui.window.width'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any, save: bool = True) -> bool:
        """设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
            save: 是否立即保存到文件
            
        Returns:
            是否设置成功
        """
        keys = key.split('.')
        config = self._config
        
        try:
            # 导航到父级字典
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # 设置值
            old_value = config.get(keys[-1])
            config[keys[-1]] = value
            
            # 发送变更信号
            if old_value != value:
                self.config_changed.emit(key, value)
            
            # 保存配置
            if save:
                return self.save_config()
            
            return True
            
        except Exception as e:
            print(f"设置配置失败: {e}")
            return False
    
    def reset_to_default(self, section: Optional[str] = None) -> bool:
        """重置配置到默认值
        
        Args:
            section: 要重置的配置节，None表示重置全部
            
        Returns:
            是否重置成功
        """
        try:
            if section:
                if section in self._default_config:
                    self._config[section] = self._default_config[section].copy()
                    self.config_changed.emit(section, self._config[section])
            else:
                self._config = self._default_config.copy()
                self.config_changed.emit("*", self._config)
            
            return self.save_config()
            
        except Exception as e:
            print(f"重置配置失败: {e}")
            return False
    
    def validate_config(self) -> Dict[str, str]:
        """验证配置有效性
        
        Returns:
            验证错误字典，键为配置项，值为错误信息
        """
        errors = {}
        
        # 验证API配置
        if not self.get('api.app_id'):
            errors['api.app_id'] = "API应用ID不能为空"
        
        if not self.get('api.api_key'):
            errors['api.api_key'] = "API密钥不能为空"
        
        # 验证窗口大小
        width = self.get('ui.window.width', 0)
        height = self.get('ui.window.height', 0)
        min_width = self.get('ui.window.min_width', 0)
        min_height = self.get('ui.window.min_height', 0)
        
        if width < min_width:
            errors['ui.window.width'] = f"窗口宽度不能小于{min_width}"
        
        if height < min_height:
            errors['ui.window.height'] = f"窗口高度不能小于{min_height}"
        
        # 验证音频设置
        max_size = self.get('audio.max_file_size_mb', 0)
        if max_size <= 0 or max_size > 50:
            errors['audio.max_file_size_mb'] = "音频文件大小限制应在1-50MB之间"
        
        return errors
    
    def export_config(self, file_path: Union[str, Path]) -> bool:
        """导出配置到文件"""
        try:
            export_config = self._config.copy()
            # 移除敏感信息
            if 'api' in export_config:
                export_config['api']['api_key'] = ""
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_config, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, file_path: Union[str, Path]) -> bool:
        """从文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 合并配置
            self._config = self._merge_config(self._default_config, imported_config)
            
            # 发送变更信号
            self.config_changed.emit("*", self._config)
            
            return self.save_config()
            
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()
    
    def is_first_run(self) -> bool:
        """检查是否首次运行"""
        return self.get('app.first_run', True)
    
    def set_first_run_completed(self) -> None:
        """标记首次运行完成"""
        self.set('app.first_run', False)
