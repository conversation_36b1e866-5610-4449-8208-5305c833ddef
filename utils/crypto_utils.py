"""
加密和签名工具模块

提供API签名、数据加密等安全功能
"""

import hashlib
import hmac
import base64
import time
import urllib.parse
from typing import Dict, Any, Optional


class CryptoUtils:
    """加密工具类
    
    提供各种加密、签名和编码功能
    """
    
    @staticmethod
    def md5_hash(data: str) -> str:
        """计算MD5哈希值
        
        Args:
            data: 要计算哈希的数据
            
        Returns:
            MD5哈希值（十六进制字符串）
        """
        return hashlib.md5(data.encode('utf-8')).hexdigest()
    
    @staticmethod
    def sha1_hash(data: str) -> str:
        """计算SHA1哈希值
        
        Args:
            data: 要计算哈希的数据
            
        Returns:
            SHA1哈希值（十六进制字符串）
        """
        return hashlib.sha1(data.encode('utf-8')).hexdigest()
    
    @staticmethod
    def sha256_hash(data: str) -> str:
        """计算SHA256哈希值
        
        Args:
            data: 要计算哈希的数据
            
        Returns:
            SHA256哈希值（十六进制字符串）
        """
        return hashlib.sha256(data.encode('utf-8')).hexdigest()
    
    @staticmethod
    def hmac_sha256(key: str, data: str) -> str:
        """计算HMAC-SHA256
        
        Args:
            key: 密钥
            data: 要签名的数据
            
        Returns:
            HMAC-SHA256签名（十六进制字符串）
        """
        return hmac.new(
            key.encode('utf-8'),
            data.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    @staticmethod
    def base64_encode(data: str) -> str:
        """Base64编码
        
        Args:
            data: 要编码的数据
            
        Returns:
            Base64编码字符串
        """
        return base64.b64encode(data.encode('utf-8')).decode('utf-8')
    
    @staticmethod
    def base64_decode(data: str) -> str:
        """Base64解码
        
        Args:
            data: Base64编码的数据
            
        Returns:
            解码后的字符串
        """
        return base64.b64decode(data.encode('utf-8')).decode('utf-8')
    
    @staticmethod
    def url_encode(data: str) -> str:
        """URL编码
        
        Args:
            data: 要编码的数据
            
        Returns:
            URL编码字符串
        """
        return urllib.parse.quote(data, safe='')
    
    @staticmethod
    def url_decode(data: str) -> str:
        """URL解码
        
        Args:
            data: URL编码的数据
            
        Returns:
            解码后的字符串
        """
        return urllib.parse.unquote(data)


class XunfeiSigner:
    """讯飞API签名器
    
    实现讯飞语音评测API的签名认证机制
    """
    
    def __init__(self, app_id: str, api_key: str, api_secret: str = ""):
        """初始化签名器
        
        Args:
            app_id: 应用ID
            api_key: API密钥
            api_secret: API密钥（某些API需要）
        """
        self.app_id = app_id
        self.api_key = api_key
        self.api_secret = api_secret
    
    def generate_signature(self, params: Dict[str, Any], timestamp: Optional[int] = None) -> Dict[str, str]:
        """生成API签名
        
        Args:
            params: 请求参数
            timestamp: 时间戳（可选，默认使用当前时间）
            
        Returns:
            包含签名信息的字典
        """
        if timestamp is None:
            timestamp = int(time.time())
        
        # 构建基础参数
        base_params = {
            'app_id': self.app_id,
            'ts': str(timestamp),
            **params
        }
        
        # 参数排序
        sorted_params = sorted(base_params.items())
        
        # 构建查询字符串
        query_string = '&'.join([f'{k}={v}' for k, v in sorted_params])
        
        # 添加API密钥
        sign_string = query_string + self.api_key
        
        # 计算MD5签名
        signature = CryptoUtils.md5_hash(sign_string)
        
        return {
            'app_id': self.app_id,
            'ts': str(timestamp),
            'sig': signature,
            **params
        }
    
    def generate_auth_header(self, method: str = "GET", url: str = "", 
                           timestamp: Optional[int] = None) -> Dict[str, str]:
        """生成认证头
        
        Args:
            method: HTTP方法
            url: 请求URL
            timestamp: 时间戳
            
        Returns:
            认证头字典
        """
        if timestamp is None:
            timestamp = int(time.time())
        
        # 构建认证字符串
        auth_string = f"{method}\n{url}\n{timestamp}"
        
        # 计算签名
        signature = CryptoUtils.hmac_sha256(self.api_secret, auth_string)
        
        # 构建认证头
        auth_header = f"api_key=\"{self.api_key}\", algorithm=\"hmac-sha256\", " \
                     f"headers=\"request-line\", signature=\"{signature}\""
        
        return {
            'Authorization': auth_header,
            'Date': time.strftime('%a, %d %b %Y %H:%M:%S GMT', time.gmtime(timestamp)),
            'Host': urllib.parse.urlparse(url).netloc if url else ''
        }


class RequestSigner:
    """通用请求签名器"""
    
    @staticmethod
    def sign_request(method: str, url: str, headers: Dict[str, str], 
                    body: str, secret_key: str) -> str:
        """签名HTTP请求
        
        Args:
            method: HTTP方法
            url: 请求URL
            headers: 请求头
            body: 请求体
            secret_key: 密钥
            
        Returns:
            请求签名
        """
        # 解析URL
        parsed_url = urllib.parse.urlparse(url)
        
        # 构建规范化请求字符串
        canonical_request = f"{method}\n{parsed_url.path}\n{parsed_url.query}\n"
        
        # 添加头部信息
        sorted_headers = sorted(headers.items())
        for key, value in sorted_headers:
            canonical_request += f"{key.lower()}:{value}\n"
        
        # 添加请求体哈希
        body_hash = CryptoUtils.sha256_hash(body)
        canonical_request += f"\n{body_hash}"
        
        # 计算签名
        signature = CryptoUtils.hmac_sha256(secret_key, canonical_request)
        
        return signature
    
    @staticmethod
    def verify_signature(signature: str, method: str, url: str, 
                        headers: Dict[str, str], body: str, secret_key: str) -> bool:
        """验证请求签名
        
        Args:
            signature: 要验证的签名
            method: HTTP方法
            url: 请求URL
            headers: 请求头
            body: 请求体
            secret_key: 密钥
            
        Returns:
            签名是否有效
        """
        expected_signature = RequestSigner.sign_request(
            method, url, headers, body, secret_key
        )
        return signature == expected_signature


class TokenManager:
    """访问令牌管理器"""
    
    def __init__(self):
        self.tokens = {}
    
    def generate_token(self, app_id: str, api_key: str, expires_in: int = 3600) -> str:
        """生成访问令牌
        
        Args:
            app_id: 应用ID
            api_key: API密钥
            expires_in: 过期时间（秒）
            
        Returns:
            访问令牌
        """
        timestamp = int(time.time())
        expires_at = timestamp + expires_in
        
        # 构建令牌数据
        token_data = f"{app_id}:{api_key}:{expires_at}"
        
        # 生成令牌
        token = CryptoUtils.base64_encode(token_data)
        
        # 存储令牌
        self.tokens[token] = {
            'app_id': app_id,
            'api_key': api_key,
            'expires_at': expires_at,
            'created_at': timestamp
        }
        
        return token
    
    def validate_token(self, token: str) -> bool:
        """验证访问令牌
        
        Args:
            token: 访问令牌
            
        Returns:
            令牌是否有效
        """
        if token not in self.tokens:
            return False
        
        token_info = self.tokens[token]
        current_time = int(time.time())
        
        return current_time < token_info['expires_at']
    
    def refresh_token(self, token: str, expires_in: int = 3600) -> Optional[str]:
        """刷新访问令牌
        
        Args:
            token: 原始令牌
            expires_in: 新的过期时间
            
        Returns:
            新的访问令牌，如果原令牌无效则返回None
        """
        if not self.validate_token(token):
            return None
        
        token_info = self.tokens[token]
        
        # 生成新令牌
        new_token = self.generate_token(
            token_info['app_id'],
            token_info['api_key'],
            expires_in
        )
        
        # 删除旧令牌
        del self.tokens[token]
        
        return new_token
    
    def revoke_token(self, token: str) -> bool:
        """撤销访问令牌
        
        Args:
            token: 要撤销的令牌
            
        Returns:
            是否成功撤销
        """
        if token in self.tokens:
            del self.tokens[token]
            return True
        return False


# 便捷函数
def md5_hash(data: str) -> str:
    """MD5哈希的便捷函数"""
    return CryptoUtils.md5_hash(data)


def base64_encode(data: str) -> str:
    """Base64编码的便捷函数"""
    return CryptoUtils.base64_encode(data)


def create_xunfei_signer(app_id: str, api_key: str, api_secret: str = "") -> XunfeiSigner:
    """创建讯飞签名器的便捷函数"""
    return XunfeiSigner(app_id, api_key, api_secret)
