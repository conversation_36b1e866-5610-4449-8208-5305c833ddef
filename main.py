#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
普通话测试软件主程序

基于PyQt6的桌面应用程序，集成讯飞语音评测API
"""

import sys
import os
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt6.QtCore import Qt, QTimer, QTranslator, QLocale
from PyQt6.QtGui import QPixmap, QFont, QIcon

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入应用程序模块
try:
    from ui.main_window import MainWindow
    from utils.config_manager import ConfigManager
    from utils.exception_handler import setup_exception_handler, log_info, log_error
    from utils.constants import APP_NAME, APP_VERSION
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有依赖已正确安装")
    sys.exit(1)


class PutonghuaTestApp:
    """普通话测试应用程序主类"""

    def __init__(self):
        self.app = None
        self.main_window = None
        self.config_manager = None
        self.splash = None

    def initialize(self):
        """初始化应用程序"""
        try:
            # 创建QApplication
            self.app = QApplication(sys.argv)
            self.app.setApplicationName(APP_NAME)
            self.app.setApplicationVersion(APP_VERSION)
            self.app.setOrganizationName("普通话测试软件开发团队")

            # 设置应用程序图标
            self.set_app_icon()

            # 设置应用程序字体
            self.set_app_font()

            # 显示启动画面
            self.show_splash_screen()

            # 初始化配置管理器
            self.config_manager = ConfigManager()

            # 设置异常处理
            setup_exception_handler()

            # 设置语言和本地化
            self.setup_localization()

            # 创建主窗口
            self.main_window = MainWindow(self.config_manager)

            # 连接信号
            self.main_window.window_closing.connect(self.on_window_closing)

            log_info(f"{APP_NAME} v{APP_VERSION} 启动成功")
            return True

        except Exception as e:
            log_error(f"应用程序初始化失败: {str(e)}")
            self.show_error_message("初始化失败", f"应用程序初始化失败:\n{str(e)}")
            return False
        
    def set_app_icon(self):
        """设置应用程序图标"""
        try:
            icon_path = project_root / "resources" / "icons" / "app.ico"
            if icon_path.exists():
                self.app.setWindowIcon(QIcon(str(icon_path)))
        except Exception as e:
            log_error(f"设置应用程序图标失败: {str(e)}")

    def set_app_font(self):
        """设置应用程序字体"""
        try:
            # 设置默认字体
            font = QFont("Microsoft YaHei", 10)
            self.app.setFont(font)
        except Exception as e:
            log_error(f"设置应用程序字体失败: {str(e)}")

    def show_splash_screen(self):
        """显示启动画面"""
        try:
            # 创建启动画面
            splash_pixmap = QPixmap(400, 300)
            splash_pixmap.fill(Qt.GlobalColor.white)

            self.splash = QSplashScreen(splash_pixmap)
            self.splash.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint)

            # 显示启动信息
            self.splash.showMessage(
                f"正在启动 {APP_NAME} v{APP_VERSION}...",
                Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter,
                Qt.GlobalColor.black
            )

            self.splash.show()
            self.app.processEvents()

        except Exception as e:
            log_error(f"显示启动画面失败: {str(e)}")

    def hide_splash_screen(self):
        """隐藏启动画面"""
        if self.splash:
            self.splash.finish(self.main_window)
            self.splash = None

    def setup_localization(self):
        """设置本地化"""
        try:
            translator = QTranslator()
            locale = QLocale.system().name()

            # 加载翻译文件（如果存在）
            translation_path = project_root / "resources" / "translations" / f"{locale}.qm"
            if translation_path.exists():
                translator.load(str(translation_path))
                self.app.installTranslator(translator)
        except Exception as e:
            log_error(f"设置本地化失败: {str(e)}")

    def show_main_window(self):
        """显示主窗口"""
        try:
            if self.main_window:
                self.main_window.show()
                self.main_window.raise_()
                self.main_window.activateWindow()

                # 延迟隐藏启动画面
                QTimer.singleShot(1000, self.hide_splash_screen)

        except Exception as e:
            log_error(f"显示主窗口失败: {str(e)}")
            self.show_error_message("启动失败", f"显示主窗口失败:\n{str(e)}")

    def run(self):
        """运行应用程序"""
        if not self.initialize():
            return 1

        try:
            # 显示主窗口
            self.show_main_window()

            # 运行事件循环
            return self.app.exec()

        except Exception as e:
            log_error(f"应用程序运行失败: {str(e)}")
            self.show_error_message("运行错误", f"应用程序运行失败:\n{str(e)}")
            return 1

    def on_window_closing(self):
        """主窗口关闭处理"""
        try:
            log_info("应用程序正在关闭...")

            # 保存配置
            if self.config_manager:
                self.config_manager.save_config()

            # 退出应用程序
            if self.app:
                self.app.quit()

        except Exception as e:
            log_error(f"应用程序关闭处理失败: {str(e)}")

    def show_error_message(self, title: str, message: str):
        """显示错误消息"""
        try:
            if self.app:
                QMessageBox.critical(None, title, message)
            else:
                print(f"错误: {title} - {message}")
        except Exception:
            print(f"错误: {title} - {message}")


def check_dependencies():
    """检查依赖项"""
    missing_deps = []

    try:
        import PyQt6
    except ImportError:
        missing_deps.append("PyQt6")

    try:
        import requests
    except ImportError:
        missing_deps.append("requests")

    try:
        import pydub
    except ImportError:
        missing_deps.append("pydub")

    if missing_deps:
        error_msg = f"缺少必要的依赖项:\n{', '.join(missing_deps)}\n\n请使用以下命令安装:\npip install {' '.join(missing_deps)}"
        print(error_msg)

        # 尝试显示图形化错误消息
        try:
            app = QApplication(sys.argv)
            QMessageBox.critical(None, "依赖项缺失", error_msg)
        except Exception:
            pass

        return False

    return True


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        error_msg = f"需要Python 3.8或更高版本，当前版本: {sys.version}"
        print(error_msg)

        try:
            app = QApplication(sys.argv)
            QMessageBox.critical(None, "Python版本不兼容", error_msg)
        except Exception:
            pass

        return False

    return True


def main():
    """主函数"""
    try:
        # 检查Python版本
        if not check_python_version():
            return 1

        # 检查依赖项
        if not check_dependencies():
            return 1

        # 创建并运行应用程序
        app = PutonghuaTestApp()
        return app.run()

    except KeyboardInterrupt:
        print("\n应用程序被用户中断")
        return 0
    except Exception as e:
        print(f"应用程序启动失败: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
