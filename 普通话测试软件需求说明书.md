# 普通话测试软件需求说明书

## 1. 项目概述

### 1.1 项目背景
开发一款基于人工智能语音评测技术的普通话测试软件，为用户提供专业、准确的普通话发音评测服务。软件采用桌面应用形式，支持音频文件上传和文本输入，提供详细的发音错误分析和可视化标注。

### 1.2 项目目标
- 提供四种普通话测试模式：读字、读词语、读文章、自由说话
- 实现多维度发音评测：声母、韵母、声调、流畅性
- 提供直观的错误标注和可视化展示
- 构建用户友好的桌面应用界面

### 1.3 技术架构
- **前端框架**：PyQt（Python GUI框架）
- **语音评测**：集成第三方语音评测API服务
- **音频处理**：支持多种音频格式的转换和处理
- **数据格式**：JSON格式的配置和结果数据

## 2. 功能需求

### 2.1 核心功能模块

#### 2.1.1 测试模块管理
**四大测试类型：**
1. **读字测试**
   - 支持单字发音评测
   - 重点评测声母、韵母、声调准确性
   - 提供字级别的详细评分

2. **读词语测试**
   - 支持词语发音评测
   - 评测词语整体发音质量
   - 分析词语内部音节连接

3. **读文章测试**
   - 支持句子和段落级别评测
   - 评测语句流畅性和完整性
   - 分析语音节奏和语调

4. **自由说话测试**
   - 支持开放式语音评测
   - 重点评测自然语流表达
   - 综合评估语音质量

#### 2.1.2 文件处理模块
**音频文件支持：**
- 支持格式：WAV, MP3, M4A, PCM
- 自动格式转换：转换为标准格式（16k采样率、16bit、单声道）
- 文件大小限制：单文件不超过5MB（约2分钟音频）
- 上传方式：拖拽上传、按钮选择、批量上传

**文本内容处理：**
- 手动文本输入：支持在界面中直接输入测试文本
- 文本文件上传：支持.txt格式文件上传
- 格式验证：自动检查文本格式和长度限制
- 字符限制：中文不超过180字节，英文不超过300字节

#### 2.1.3 语音评测模块
**评测维度：**
1. **声母评测**：检测声母发音准确性
2. **韵母评测**：检测韵母发音准确性  
3. **声调评测**：检测声调标准程度
4. **流畅性评测**：检测语音流畅度和自然度

**评测结果：**
- 总体评分：0-100分制综合评分
- 分维度评分：各维度独立评分
- 错误定位：精确到字/词级别的错误位置
- 错误分类：按错误类型进行分类标注

#### 2.1.4 结果展示模块
**错误标注系统：**
- **声母错误**：红色下划线 + "声" 标记
- **韵母错误**：蓝色下划线 + "韵" 标记
- **声调错误**：橙色波浪线 + 声调符号
- **漏读标记**：灰色背景 + "漏" 标记
- **流畅性问题**：黄色高亮 + "流" 标记

**交互功能：**
- 鼠标悬停显示详细错误信息
- 分维度显示/隐藏错误标注
- 错误统计和分析报告
- 评分结果可视化图表

### 2.2 用户界面设计

#### 2.2.1 主界面布局
**界面结构：**
- **顶部区域**：测试模块选择、进度指示器
- **左侧区域**：文件上传区域、测试文本输入区域
- **中央区域**：测试内容显示、错误标注展示
- **右侧区域**：评分结果、错误统计、设置面板
- **底部区域**：状态栏、操作按钮

#### 2.2.2 文件上传界面
**上传区域设计：**
- 拖拽上传区域：支持直接拖拽音频文件
- 文件选择按钮：打开系统文件选择对话框
- 上传进度显示：实时显示文件上传进度
- 文件信息展示：显示文件名、大小、格式等信息

#### 2.2.3 结果展示界面
**富文本标注区域：**
- 使用QTextEdit组件实现富文本显示
- 支持HTML/CSS样式的错误标注
- 可滚动的文本显示区域
- 支持文本选择和复制功能

**评分面板：**
- 总分显示：大字体突出显示总体评分
- 分维度评分：声母、韵母、声调、流畅性分别显示
- 评分等级：优秀、良好、及格、不及格等级显示
- 进步建议：基于评测结果提供改进建议

### 2.3 系统配置功能

#### 2.3.1 评测设置
- **评测语种**：中文普通话/英文（可扩展）
- **评测模式**：标准模式/详细模式
- **评分严格程度**：宽松/标准/严格三个等级
- **评测参数**：各维度权重调整

#### 2.3.2 显示设置
- **字体设置**：字体大小（小/中/大）
- **标注样式**：错误标注颜色和样式自定义
- **显示模式**：总分显示/分维度显示/详细显示
- **界面主题**：支持明亮/暗色主题切换

#### 2.3.3 文件设置
- **工作目录**：设置默认的文件保存路径
- **音频质量**：音频处理质量设置（标准/高质量）
- **文件管理**：是否保存处理记录和历史文件
- **导出设置**：结果导出格式和路径设置

## 3. 技术实现方案

### 3.1 系统架构设计

#### 3.1.1 模块划分
1. **主窗口模块**：整体界面框架和布局管理
2. **文件处理模块**：音频和文本文件的上传、转换、验证
3. **API调用模块**：语音评测API的集成和调用管理
4. **数据解析模块**：评测结果的解析和数据处理
5. **界面展示模块**：错误标注和结果可视化展示
6. **配置管理模块**：系统设置和用户配置管理

#### 3.1.2 数据流设计
```
用户上传文件 → 文件验证和转换 → API调用评测 → 结果解析 → 界面标注展示
     ↓              ↓              ↓           ↓           ↓
  文件管理      格式转换        网络通信     数据处理    界面渲染
```

### 3.2 核心技术实现

#### 3.2.1 音频处理技术
- **格式转换**：使用FFmpeg或类似库进行音频格式转换
- **质量检测**：音频时长、音量、采样率等参数检测
- **编码处理**：Base64编码和URL编码处理
- **批量处理**：支持多文件并行处理

#### 3.2.2 API集成技术
- **HTTP请求**：使用requests库进行API调用
- **认证机制**：API密钥管理和签名验证
- **错误处理**：网络异常、超时、API错误的处理机制
- **并发控制**：API调用频率限制和并发管理

#### 3.2.3 界面渲染技术
- **富文本处理**：QTextEdit的HTML渲染和样式控制
- **动态标注**：基于位置信息的动态错误标注
- **交互响应**：鼠标事件处理和悬停提示
- **性能优化**：大文本的分页和懒加载

### 3.3 数据处理方案

#### 3.3.1 API结果解析
**JSON数据结构处理：**
- 解析评测总分和各维度分数
- 提取错误位置信息（beg_pos, end_pos）
- 分类错误类型和严重程度
- 生成标注数据结构

#### 3.3.2 错误标注算法
**位置映射算法：**
- 将API返回的位置信息映射到原文本
- 处理多字节字符的位置计算
- 支持重叠错误的标注处理
- 优化标注性能和准确性

## 4. 非功能需求

### 4.1 性能要求
- **响应时间**：界面操作响应时间不超过200ms
- **处理能力**：支持最大5MB音频文件处理
- **并发处理**：支持最多3个文件同时处理
- **内存使用**：运行时内存占用不超过500MB

### 4.2 兼容性要求
- **操作系统**：Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **Python版本**：Python 3.8+
- **依赖库**：PyQt5/6, requests, json等标准库
- **音频格式**：WAV, MP3, M4A, PCM等主流格式

### 4.3 安全性要求
- **数据保护**：用户音频和文本数据的本地保护
- **API安全**：API密钥的安全存储和传输
- **隐私保护**：不保存用户敏感信息
- **错误处理**：异常情况的安全处理机制

### 4.4 可用性要求
- **界面友好**：直观易用的用户界面设计
- **操作简便**：最少3步完成一次完整测试
- **错误提示**：清晰的错误信息和操作指导
- **帮助文档**：内置使用说明和常见问题解答

## 5. 项目交付物

### 5.1 软件交付
- **可执行程序**：打包后的桌面应用程序
- **源代码**：完整的Python源代码
- **配置文件**：系统配置和设置文件
- **依赖清单**：requirements.txt依赖列表

### 5.2 文档交付
- **用户手册**：软件使用说明文档
- **技术文档**：代码结构和API说明
- **部署指南**：软件安装和配置指南
- **维护手册**：软件维护和更新指南

### 5.3 测试交付
- **测试用例**：功能测试和性能测试用例
- **测试报告**：软件测试结果报告
- **示例文件**：测试用的音频和文本示例
- **问题清单**：已知问题和解决方案

---

**文档版本**：v1.0  
**编写日期**：2025年1月  
**最后更新**：2025年1月
