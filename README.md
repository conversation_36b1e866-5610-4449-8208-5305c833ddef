# 普通话测试软件

基于PyQt6和AI语音评测技术的普通话发音测试桌面应用程序。

## 功能特性

- 四种测试模式：读字、读词语、读文章、自由说话
- 多维度评测：声母、韵母、声调、流畅性
- 智能错误标注和可视化展示
- 支持多种音频格式：WAV、MP3、M4A、PCM
- 用户友好的图形界面

## 项目结构

```
putonghua_test/
├── main.py                 # 应用程序入口
├── requirements.txt        # 依赖清单
├── config.json            # 配置文件
├── ui/                    # 界面模块
│   └── __init__.py
├── core/                  # 核心业务逻辑
│   └── __init__.py
├── utils/                 # 工具函数
│   └── __init__.py
└── resources/             # 资源文件
    ├── icons/
    ├── styles/
    └── samples/
```

## 安装和运行

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 配置API密钥：
编辑 `config.json` 文件，填入您的API配置信息。

3. 运行应用程序：
```bash
python main.py
```

## 开发环境要求

- Python 3.8+
- PyQt6
- FFmpeg（用于音频处理）

## 技术架构

- **GUI框架**: PyQt6
- **音频处理**: pydub + FFmpeg
- **HTTP请求**: requests
- **配置管理**: JSON
- **异步处理**: QThread

## 许可证

本项目采用MIT许可证。详见LICENSE文件。
