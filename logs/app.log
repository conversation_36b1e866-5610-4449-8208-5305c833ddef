2025-08-03 13:10:46,067 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 13:10:52,285 - PutonghuaTest - ERROR - 未捕获的异常: Traceback (most recent call last):
  File "G:\Code\Outsourcing\普通话测试\ui\main_window.py", line 354, in show_settings
    if show_settings_dialog(self.config_manager, self):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 417, in show_settings_dialog
    dialog = SettingsDialog(config_manager, parent)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 325, in __init__
    self.setup_ui()
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 343, in setup_ui
    self.general_settings = GeneralSettingsWidget(self.config_manager)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 180, in __init__
    super().__init__(parent)
  File "G:\Code\Outsourcing\普通话测试\ui\base_widget.py", line 23, in __init__
    self.setup_ui()
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 255, in setup_ui
    self.load_settings()
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 275, in load_settings
    self.font_size_spin.setValue(self.config_manager.get('ui.font_size', 12))
TypeError: setValue(self, val: int): argument 1 has unexpected type 'str'

2025-08-03 13:11:52,833 - PutonghuaTest - INFO - 应用程序正在关闭...
2025-08-03 13:12:40,196 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 13:13:02,401 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 13:13:34,159 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 13:13:42,954 - PutonghuaTest - ERROR - 未捕获的异常: Traceback (most recent call last):
  File "G:\Code\Outsourcing\普通话测试\ui\main_window.py", line 296, in clear_results
    self.result_widget.clear_result()
  File "G:\Code\Outsourcing\普通话测试\ui\result_widget.py", line 526, in clear_result
    self.error_detail._show_no_error()
  File "G:\Code\Outsourcing\普通话测试\ui\result_widget.py", line 225, in _show_no_error
    self.detail_layout.itemAt(i).widget().setParent(None)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'setParent'

2025-08-03 13:13:45,591 - PutonghuaTest - INFO - 应用程序正在关闭...
2025-08-03 13:13:55,071 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 13:14:19,955 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 13:19:20,682 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 13:19:23,008 - PutonghuaTest - INFO - 音频处理完成
2025-08-03 13:19:27,739 - PutonghuaTest - INFO - 音频处理完成
2025-08-03 13:19:32,267 - PutonghuaTest - INFO - 音频处理完成
2025-08-03 13:19:34,515 - PutonghuaTest - INFO - 音频处理完成
2025-08-03 13:19:37,327 - PutonghuaTest - INFO - API签名器初始化成功: app_id=c94ba137
2025-08-03 13:19:37,327 - PutonghuaTest - INFO - API工作线程初始化成功
2025-08-03 13:19:37,327 - PutonghuaTest - ERROR - 音频处理失败: name 'Path' is not defined
2025-08-03 13:19:37,329 - PutonghuaTest - INFO - API工作线程开始运行
2025-08-03 13:19:39,346 - PutonghuaTest - INFO - 正在停止API工作线程...
2025-08-03 13:19:39,346 - PutonghuaTest - INFO - API工作线程已停止
2025-08-03 13:19:40,259 - PutonghuaTest - INFO - 应用程序正在关闭...
2025-08-03 13:19:50,397 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 13:19:56,686 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 13:20:06,762 - PutonghuaTest - INFO - 音频处理完成
2025-08-03 13:20:12,058 - PutonghuaTest - INFO - API签名器初始化成功: app_id=c94ba137
2025-08-03 13:20:12,058 - PutonghuaTest - INFO - API工作线程初始化成功
2025-08-03 13:20:12,058 - PutonghuaTest - ERROR - 音频处理失败: name 'Path' is not defined
2025-08-03 13:20:12,073 - PutonghuaTest - INFO - API工作线程开始运行
2025-08-03 13:20:53,747 - PutonghuaTest - INFO - 正在停止API工作线程...
2025-08-03 13:20:53,747 - PutonghuaTest - INFO - API工作线程已停止
2025-08-03 13:21:16,067 - PutonghuaTest - INFO - API签名器初始化成功: app_id=c94ba137
2025-08-03 13:21:16,067 - PutonghuaTest - INFO - API工作线程初始化成功
2025-08-03 13:21:16,067 - PutonghuaTest - ERROR - 音频处理失败: name 'Path' is not defined
2025-08-03 13:21:16,068 - PutonghuaTest - INFO - API工作线程开始运行
2025-08-03 13:21:17,323 - PutonghuaTest - INFO - 正在停止API工作线程...
2025-08-03 13:21:17,323 - PutonghuaTest - INFO - API工作线程已停止
2025-08-03 13:24:47,302 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 13:24:51,034 - PutonghuaTest - INFO - 应用程序正在关闭...
2025-08-03 13:24:53,437 - PutonghuaTest - INFO - 应用程序正在关闭...
2025-08-03 13:24:58,761 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 13:25:05,037 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 13:25:09,721 - PutonghuaTest - INFO - API签名器初始化成功: app_id=c94ba137
2025-08-03 13:25:09,722 - PutonghuaTest - INFO - API工作线程初始化成功
2025-08-03 13:25:09,722 - PutonghuaTest - ERROR - 音频处理失败: name 'Path' is not defined
2025-08-03 13:25:09,723 - PutonghuaTest - INFO - API工作线程开始运行
2025-08-03 13:25:10,674 - PutonghuaTest - INFO - 正在停止API工作线程...
2025-08-03 13:25:10,674 - PutonghuaTest - INFO - API工作线程已停止
2025-08-03 13:35:20,296 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 13:35:30,108 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 13:35:46,729 - PutonghuaTest - INFO - API签名器初始化成功: app_id=c94ba137
2025-08-03 13:35:46,730 - PutonghuaTest - INFO - API工作线程初始化成功
2025-08-03 13:35:46,730 - PutonghuaTest - ERROR - 音频处理失败: name 'Path' is not defined
2025-08-03 13:35:46,730 - PutonghuaTest - INFO - API工作线程开始运行
2025-08-03 13:35:53,240 - PutonghuaTest - INFO - 正在停止API工作线程...
2025-08-03 13:35:53,240 - PutonghuaTest - INFO - API工作线程已停止
2025-08-03 13:38:23,526 - PutonghuaTest - INFO - 应用程序正在关闭...
2025-08-03 13:38:28,833 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 13:38:36,768 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 13:38:49,474 - PutonghuaTest - INFO - API签名器初始化成功: app_id=c94ba137
2025-08-03 13:38:49,474 - PutonghuaTest - INFO - API工作线程初始化成功
2025-08-03 13:38:49,476 - PutonghuaTest - INFO - API工作线程开始运行
2025-08-03 13:38:50,741 - PutonghuaTest - ERROR - API调用失败: 音频数据为空
2025-08-03 13:38:53,820 - PutonghuaTest - INFO - 正在停止API工作线程...
2025-08-03 13:38:53,820 - PutonghuaTest - INFO - API工作线程已停止
2025-08-03 13:44:20,773 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 13:44:25,939 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 13:44:34,548 - PutonghuaTest - INFO - API签名器初始化成功: app_id=c94ba137
2025-08-03 13:44:34,549 - PutonghuaTest - INFO - API工作线程初始化成功
2025-08-03 13:44:34,551 - PutonghuaTest - INFO - API工作线程开始运行
2025-08-03 13:44:34,557 - PutonghuaTest - ERROR - 未捕获的异常: Traceback (most recent call last):
  File "G:\Code\Outsourcing\普通话测试\core\audio_processor.py", line 279, in run
    log_info(f"开始处理音频文件: {self.file_path}")
    ^^^^^^^^
NameError: name 'log_info' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "G:\Code\Outsourcing\普通话测试\core\audio_processor.py", line 292, in run
    log_error(f"音频处理异常: {self.file_path} - {error_msg}")
    ^^^^^^^^^
NameError: name 'log_error' is not defined

2025-08-03 13:45:55,211 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 13:46:00,789 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 13:46:06,430 - PutonghuaTest - INFO - API签名器初始化成功: app_id=c94ba137
2025-08-03 13:46:06,430 - PutonghuaTest - INFO - API工作线程初始化成功
2025-08-03 13:46:06,433 - PutonghuaTest - INFO - API工作线程开始运行
2025-08-03 13:46:06,435 - PutonghuaTest - INFO - 开始处理音频文件: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 13:46:07,508 - PutonghuaTest - INFO - 音频处理成功: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 13:46:07,508 - PutonghuaTest - INFO - API请求已添加到队列: req_1754199967508, 队列长度: 1
2025-08-03 13:46:07,509 - PutonghuaTest - INFO - API请求已添加到队列: req_1754199967509, 队列长度: 2
2025-08-03 13:46:07,509 - PutonghuaTest - INFO - 开始处理API请求: req_1754199967508
2025-08-03 13:46:07,509 - PutonghuaTest - INFO - 开始语音评测请求: req_1754199967508
2025-08-03 13:46:12,292 - PutonghuaTest - ERROR - API请求处理失败: req_1754199967508 - 请求处理失败: API返回错误: 10106 - invalid parameter|invalid Headprams
2025-08-03 13:46:12,292 - PutonghuaTest - INFO - 开始处理API请求: req_1754199967509
2025-08-03 13:46:12,292 - PutonghuaTest - INFO - 开始语音评测请求: req_1754199967509
2025-08-03 13:46:12,293 - PutonghuaTest - ERROR - API请求失败: req_1754199967508, 错误: 请求处理失败: API返回错误: 10106 - invalid parameter|invalid Headprams, 代码: -4
2025-08-03 13:46:12,293 - PutonghuaTest - ERROR - 评测失败: req_1754199967508 - 请求处理失败: API返回错误: 10106 - invalid parameter|invalid Headprams (代码: -4)
2025-08-03 13:46:13,830 - PutonghuaTest - ERROR - API请求处理失败: req_1754199967509 - 请求处理失败: API返回错误: 10106 - invalid parameter|invalid Headprams
2025-08-03 13:46:13,830 - PutonghuaTest - ERROR - API请求失败: req_1754199967509, 错误: 请求处理失败: API返回错误: 10106 - invalid parameter|invalid Headprams, 代码: -4
2025-08-03 13:46:13,830 - PutonghuaTest - ERROR - 评测失败: req_1754199967509 - 请求处理失败: API返回错误: 10106 - invalid parameter|invalid Headprams (代码: -4)
2025-08-03 13:51:14,332 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 13:53:29,955 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 13:53:36,169 - PutonghuaTest - INFO - API签名器初始化成功: app_id=c94ba137
2025-08-03 13:53:36,169 - PutonghuaTest - INFO - API工作线程初始化成功
2025-08-03 13:53:36,171 - PutonghuaTest - INFO - API工作线程开始运行
2025-08-03 13:53:36,173 - PutonghuaTest - INFO - 开始处理音频文件: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 13:53:37,089 - PutonghuaTest - INFO - 音频处理成功: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 13:53:37,089 - PutonghuaTest - INFO - API请求已添加到队列: req_1754200417089, 队列长度: 1
2025-08-03 13:53:37,089 - PutonghuaTest - INFO - 开始处理API请求: req_1754200417089
2025-08-03 13:53:37,090 - PutonghuaTest - INFO - 开始语音评测请求: req_1754200417089
2025-08-03 13:53:37,090 - PutonghuaTest - INFO - API请求已添加到队列: req_1754200417090, 队列长度: 1
2025-08-03 13:53:43,260 - PutonghuaTest - ERROR - API请求处理失败: req_1754200417089 - 请求处理失败: API返回错误: 10106 - invalid parameter|invalid Headprams
2025-08-03 13:53:43,261 - PutonghuaTest - INFO - 开始处理API请求: req_1754200417090
2025-08-03 13:53:43,261 - PutonghuaTest - ERROR - API请求失败: req_1754200417089, 错误: 请求处理失败: API返回错误: 10106 - invalid parameter|invalid Headprams, 代码: -4
2025-08-03 13:53:43,261 - PutonghuaTest - ERROR - 评测失败: req_1754200417089 - 请求处理失败: API返回错误: 10106 - invalid parameter|invalid Headprams (代码: -4)
2025-08-03 13:53:43,262 - PutonghuaTest - INFO - 开始语音评测请求: req_1754200417090
2025-08-03 13:53:44,657 - PutonghuaTest - ERROR - API请求处理失败: req_1754200417090 - 请求处理失败: API返回错误: 10106 - invalid parameter|invalid Headprams
2025-08-03 13:53:44,657 - PutonghuaTest - ERROR - API请求失败: req_1754200417090, 错误: 请求处理失败: API返回错误: 10106 - invalid parameter|invalid Headprams, 代码: -4
2025-08-03 13:53:44,657 - PutonghuaTest - ERROR - 评测失败: req_1754200417090 - 请求处理失败: API返回错误: 10106 - invalid parameter|invalid Headprams (代码: -4)
2025-08-03 14:05:36,153 - PutonghuaTest - INFO - 正在停止API工作线程...
2025-08-03 14:05:36,153 - PutonghuaTest - INFO - API工作线程已停止
2025-08-03 14:05:36,488 - PutonghuaTest - INFO - 应用程序正在关闭...
2025-08-03 14:11:55,413 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 14:12:46,394 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 14:12:53,605 - PutonghuaTest - INFO - WebSocket API工作线程开始运行
2025-08-03 14:12:53,605 - PutonghuaTest - INFO - WebSocket API工作线程结束
2025-08-03 14:12:53,607 - PutonghuaTest - INFO - 开始处理音频文件: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 14:12:54,627 - PutonghuaTest - INFO - 音频处理成功: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 14:12:54,627 - PutonghuaTest - INFO - WebSocket API请求已添加到队列: req_1754201574627
2025-08-03 14:24:53,469 - PutonghuaTest - INFO - WebSocket API工作线程开始运行
2025-08-03 14:24:53,469 - PutonghuaTest - INFO - WebSocket API工作线程结束
2025-08-03 14:24:53,469 - PutonghuaTest - INFO - 开始处理音频文件: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 14:24:54,530 - PutonghuaTest - INFO - 音频处理成功: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 14:24:54,530 - PutonghuaTest - INFO - WebSocket API请求已添加到队列: req_1754202294530
2025-08-03 14:28:22,000 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 14:28:26,430 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 14:29:02,460 - PutonghuaTest - INFO - WebSocket API工作线程开始运行
2025-08-03 14:29:02,461 - PutonghuaTest - INFO - WebSocket API工作线程结束
2025-08-03 14:29:02,463 - PutonghuaTest - INFO - 开始处理音频文件: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 14:29:03,911 - PutonghuaTest - INFO - 音频处理成功: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 14:29:04,221 - PutonghuaTest - INFO - WebSocket API请求已添加到队列: req_1754202544221
2025-08-03 14:35:18,950 - PutonghuaTest - INFO - 应用程序正在关闭...
2025-08-03 14:35:24,594 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 14:36:52,949 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 14:36:59,134 - PutonghuaTest - INFO - WebSocket API工作线程开始运行
2025-08-03 14:36:59,136 - PutonghuaTest - INFO - 开始处理音频文件: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 14:37:00,120 - PutonghuaTest - INFO - 音频处理成功: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 14:37:00,121 - PutonghuaTest - INFO - WebSocket API请求已添加到队列: req_1754203020121, 队列长度: 1
2025-08-03 14:37:00,124 - PutonghuaTest - INFO - 开始处理WebSocket API请求: req_1754203020121
2025-08-03 14:37:00,144 - PutonghuaTest - ERROR - WebSocket请求失败: WebSocketApp.run_forever() got an unexpected keyword argument 'timeout'
2025-08-03 14:37:00,144 - PutonghuaTest - ERROR - WebSocket API请求处理失败: req_1754203020121 - WebSocket API请求失败: WebSocketApp.run_forever() got an unexpected keyword argument 'timeout'
2025-08-03 14:37:00,145 - PutonghuaTest - ERROR - 评测失败: req_1754203020121 - WebSocket API请求失败: WebSocketApp.run_forever() got an unexpected keyword argument 'timeout' (代码: -1)
2025-08-03 14:37:11,010 - PutonghuaTest - INFO - WebSocket API工作线程停止
2025-08-03 14:37:11,065 - PutonghuaTest - INFO - WebSocket API工作线程结束
2025-08-03 14:37:11,423 - PutonghuaTest - INFO - 应用程序正在关闭...
2025-08-03 14:46:14,091 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 14:46:18,374 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 14:46:26,853 - PutonghuaTest - INFO - WebSocket API工作线程开始运行
2025-08-03 14:46:26,855 - PutonghuaTest - INFO - 开始处理音频文件: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 14:46:27,729 - PutonghuaTest - INFO - 音频处理成功: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 14:46:27,729 - PutonghuaTest - INFO - WebSocket API请求已添加到队列: req_1754203587729, 队列长度: 1
2025-08-03 14:46:27,730 - PutonghuaTest - INFO - 开始处理WebSocket API请求: req_1754203587729
2025-08-03 14:46:27,955 - PutonghuaTest - ERROR - WebSocket连接错误: Handshake status 401 Unauthorized -+-+- {'date': 'Sun, 03 Aug 2025 06:46:29 GMT', 'content-type': 'application/json; charset=utf-8', 'connection': 'keep-alive', 'content-length': '76', 'server': 'kong/1.3.0'} -+-+- b'{"message":"HMAC signature cannot be verified: fail to retrieve credential"}'
2025-08-03 14:46:27,955 - PutonghuaTest - INFO - WebSocket连接已关闭
2025-08-03 14:46:27,955 - PutonghuaTest - ERROR - WebSocket请求失败: WebSocket错误: Handshake status 401 Unauthorized -+-+- {'date': 'Sun, 03 Aug 2025 06:46:29 GMT', 'content-type': 'application/json; charset=utf-8', 'connection': 'keep-alive', 'content-length': '76', 'server': 'kong/1.3.0'} -+-+- b'{"message":"HMAC signature cannot be verified: fail to retrieve credential"}'
2025-08-03 14:46:27,955 - PutonghuaTest - ERROR - WebSocket API请求处理失败: req_1754203587729 - WebSocket API请求失败: WebSocket错误: Handshake status 401 Unauthorized -+-+- {'date': 'Sun, 03 Aug 2025 06:46:29 GMT', 'content-type': 'application/json; charset=utf-8', 'connection': 'keep-alive', 'content-length': '76', 'server': 'kong/1.3.0'} -+-+- b'{"message":"HMAC signature cannot be verified: fail to retrieve credential"}'
2025-08-03 14:46:32,973 - PutonghuaTest - INFO - WebSocket API工作线程停止
2025-08-03 14:46:32,976 - PutonghuaTest - INFO - WebSocket API工作线程结束
2025-08-03 14:46:35,729 - PutonghuaTest - INFO - WebSocket API工作线程开始运行
2025-08-03 14:46:35,729 - PutonghuaTest - INFO - 开始处理音频文件: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 14:46:36,527 - PutonghuaTest - INFO - 音频处理成功: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 14:46:36,528 - PutonghuaTest - INFO - WebSocket API请求已添加到队列: req_1754203596528, 队列长度: 1
2025-08-03 14:46:36,612 - PutonghuaTest - INFO - 开始处理WebSocket API请求: req_1754203596528
2025-08-03 14:46:36,755 - PutonghuaTest - ERROR - WebSocket连接错误: Handshake status 401 Unauthorized -+-+- {'date': 'Sun, 03 Aug 2025 06:46:38 GMT', 'content-type': 'application/json; charset=utf-8', 'connection': 'keep-alive', 'content-length': '76', 'server': 'kong/1.3.0'} -+-+- b'{"message":"HMAC signature cannot be verified: fail to retrieve credential"}'
2025-08-03 14:46:36,755 - PutonghuaTest - INFO - WebSocket连接已关闭
2025-08-03 14:46:36,755 - PutonghuaTest - ERROR - WebSocket请求失败: WebSocket错误: Handshake status 401 Unauthorized -+-+- {'date': 'Sun, 03 Aug 2025 06:46:38 GMT', 'content-type': 'application/json; charset=utf-8', 'connection': 'keep-alive', 'content-length': '76', 'server': 'kong/1.3.0'} -+-+- b'{"message":"HMAC signature cannot be verified: fail to retrieve credential"}'
2025-08-03 14:46:36,755 - PutonghuaTest - ERROR - WebSocket API请求处理失败: req_1754203596528 - WebSocket API请求失败: WebSocket错误: Handshake status 401 Unauthorized -+-+- {'date': 'Sun, 03 Aug 2025 06:46:38 GMT', 'content-type': 'application/json; charset=utf-8', 'connection': 'keep-alive', 'content-length': '76', 'server': 'kong/1.3.0'} -+-+- b'{"message":"HMAC signature cannot be verified: fail to retrieve credential"}'
2025-08-03 14:51:25,423 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 14:51:29,436 - PutonghuaTest - ERROR - 未捕获的异常: Traceback (most recent call last):
  File "G:\Code\Outsourcing\普通话测试\ui\main_window.py", line 355, in show_settings
    if show_settings_dialog(self.config_manager, self):
       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 432, in show_settings_dialog
    dialog = SettingsDialog(config_manager, parent)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 340, in __init__
    self.setup_ui()
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 358, in setup_ui
    self.general_settings = GeneralSettingsWidget(self.config_manager)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 195, in __init__
    super().__init__(parent)
  File "G:\Code\Outsourcing\普通话测试\ui\base_widget.py", line 23, in __init__
    self.setup_ui()
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 270, in setup_ui
    self.load_settings()
  File "G:\Code\Outsourcing\普通话测试\ui\settings_dialog.py", line 290, in load_settings
    self.font_size_spin.setValue(self.config_manager.get('ui.font_size', 12))
TypeError: setValue(self, val: int): argument 1 has unexpected type 'str'

2025-08-03 14:51:31,898 - PutonghuaTest - INFO - 应用程序正在关闭...
2025-08-03 14:51:36,212 - PutonghuaTest - INFO - 普通话测试软件 v1.0.0 启动成功
2025-08-03 14:51:40,956 - PutonghuaTest - INFO - 文件上传完成: 1 个文件
2025-08-03 14:51:48,482 - PutonghuaTest - INFO - WebSocket API工作线程开始运行
2025-08-03 14:51:48,485 - PutonghuaTest - INFO - 开始处理音频文件: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 14:51:49,624 - PutonghuaTest - INFO - 音频处理成功: G:\Code\Outsourcing\普通话测试\01.MP3.mp3
2025-08-03 14:51:49,624 - PutonghuaTest - INFO - WebSocket API请求已添加到队列: req_1754203909624, 队列长度: 1
2025-08-03 14:51:49,696 - PutonghuaTest - INFO - 开始处理WebSocket API请求: req_1754203909624
2025-08-03 14:51:49,900 - PutonghuaTest - ERROR - WebSocket连接错误: Handshake status 401 Unauthorized -+-+- {'date': 'Sun, 03 Aug 2025 06:51:51 GMT', 'content-type': 'application/json; charset=utf-8', 'connection': 'keep-alive', 'content-length': '76', 'server': 'kong/1.3.0'} -+-+- b'{"message":"HMAC signature cannot be verified: fail to retrieve credential"}'
2025-08-03 14:51:49,900 - PutonghuaTest - INFO - WebSocket连接已关闭
2025-08-03 14:51:49,900 - PutonghuaTest - ERROR - WebSocket请求失败: WebSocket错误: Handshake status 401 Unauthorized -+-+- {'date': 'Sun, 03 Aug 2025 06:51:51 GMT', 'content-type': 'application/json; charset=utf-8', 'connection': 'keep-alive', 'content-length': '76', 'server': 'kong/1.3.0'} -+-+- b'{"message":"HMAC signature cannot be verified: fail to retrieve credential"}'
2025-08-03 14:51:49,900 - PutonghuaTest - ERROR - WebSocket API请求处理失败: req_1754203909624 - WebSocket API请求失败: WebSocket错误: Handshake status 401 Unauthorized -+-+- {'date': 'Sun, 03 Aug 2025 06:51:51 GMT', 'content-type': 'application/json; charset=utf-8', 'connection': 'keep-alive', 'content-length': '76', 'server': 'kong/1.3.0'} -+-+- b'{"message":"HMAC signature cannot be verified: fail to retrieve credential"}'
