"""
评测结果解析器模块

解析讯飞API返回的评测结果，转换为标准数据模型
"""

import json
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from .data_models import (
    EvaluationResult, ScoreInfo, SentenceResult, WordResult, 
    ErrorInfo, ErrorPosition, ErrorSeverity
)
from utils.constants import TestType, Language, ErrorType
from utils.text_utils import TextProcessor, PositionMapper, byte_to_char_pos
from utils.exception_handler import log_info, log_warning, log_error


class ResultParser:
    """评测结果解析器
    
    解析讯飞API返回的JSON数据，转换为标准化的数据模型
    """
    
    def __init__(self):
        self.error_type_mapping = self._build_error_type_mapping()
        self.severity_mapping = self._build_severity_mapping()
    
    def _build_error_type_mapping(self) -> Dict[str, ErrorType]:
        """构建错误类型映射"""
        return {
            'phone': ErrorType.INITIAL_CONSONANT,  # 声母错误
            'tone': ErrorType.TONE,                # 声调错误
            'fluency': ErrorType.FLUENCY,          # 流畅性错误
            'missing': ErrorType.MISSING,          # 漏读
            'initial': ErrorType.INITIAL_CONSONANT, # 声母
            'final': ErrorType.FINAL_SOUND,       # 韵母
            'rhythm': ErrorType.FLUENCY,          # 节奏
            'speed': ErrorType.FLUENCY,           # 语速
        }
    
    def _build_severity_mapping(self) -> Dict[float, ErrorSeverity]:
        """构建严重程度映射（基于分数）"""
        return {
            0.0: ErrorSeverity.CRITICAL,   # 0-30分：严重错误
            30.0: ErrorSeverity.MAJOR,     # 30-60分：主要错误
            60.0: ErrorSeverity.MINOR,     # 60-80分：轻微错误
            80.0: ErrorSeverity.WARNING,   # 80-100分：警告
        }
    
    def parse_evaluation_result(self, api_response: Dict[str, Any], 
                              request_id: str, test_type: TestType, 
                              language: Language, original_text: str,
                              audio_duration: Optional[float] = None,
                              processing_start_time: Optional[float] = None) -> EvaluationResult:
        """解析完整的评测结果
        
        Args:
            api_response: API响应数据
            request_id: 请求ID
            test_type: 测试类型
            language: 语言类型
            original_text: 原始文本
            audio_duration: 音频时长
            processing_start_time: 处理开始时间
            
        Returns:
            解析后的评测结果
        """
        try:
            log_info(f"开始解析评测结果: {request_id}")
            
            # 创建结果对象
            result = EvaluationResult(
                request_id=request_id,
                test_type=test_type,
                language=language,
                original_text=original_text,
                audio_duration=audio_duration,
                api_response_raw=api_response
            )
            
            # 计算处理时间
            if processing_start_time:
                result.processing_time = time.time() - processing_start_time
            
            # 解析评分信息
            result.scores = self._parse_scores(api_response)
            
            # 解析详细结果
            if 'data' in api_response:
                data = api_response['data']
                
                # 根据测试类型解析不同的结果结构
                if test_type in [TestType.READ_SYLLABLE, TestType.READ_WORD]:
                    result.sentences = self._parse_word_level_results(data, original_text)
                elif test_type in [TestType.READ_SENTENCE, TestType.READ_CHAPTER]:
                    result.sentences = self._parse_sentence_level_results(data, original_text)
                
                # 解析全局错误
                result.global_errors = self._parse_global_errors(data, original_text)
            
            log_info(f"评测结果解析完成: {request_id}, 总分: {result.scores.total_score}")
            return result
            
        except Exception as e:
            log_error(f"解析评测结果失败: {request_id} - {str(e)}")
            # 返回基础结果对象，避免完全失败
            return EvaluationResult(
                request_id=request_id,
                test_type=test_type,
                language=language,
                original_text=original_text,
                audio_duration=audio_duration,
                api_response_raw=api_response
            )
    
    def _parse_scores(self, api_response: Dict[str, Any]) -> ScoreInfo:
        """解析评分信息
        
        Args:
            api_response: API响应数据
            
        Returns:
            评分信息对象
        """
        scores = ScoreInfo()
        
        try:
            # 从不同可能的位置提取分数
            data = api_response.get('data', {})
            
            # 总分
            scores.total_score = float(data.get('total_score', 0.0))
            
            # 各维度分数
            scores.phone_score = self._safe_float(data.get('phone_score'))
            scores.tone_score = self._safe_float(data.get('tone_score'))
            scores.integrity_score = self._safe_float(data.get('integrity_score'))
            scores.fluency_score = self._safe_float(data.get('fluency_score'))
            
            # 细分分数
            scores.initial_consonant_score = self._safe_float(data.get('initial_score'))
            scores.final_sound_score = self._safe_float(data.get('final_score'))
            
            # 如果没有细分分数，尝试从phone_score推算
            if scores.phone_score and not scores.initial_consonant_score:
                scores.initial_consonant_score = scores.phone_score
                scores.final_sound_score = scores.phone_score
            
        except Exception as e:
            log_warning(f"解析评分信息时发生错误: {str(e)}")
        
        return scores
    
    def _parse_word_level_results(self, data: Dict[str, Any], original_text: str) -> List[SentenceResult]:
        """解析单词级别的结果（读字、读词语）
        
        Args:
            data: API数据
            original_text: 原始文本
            
        Returns:
            句子结果列表
        """
        sentences = []
        
        try:
            # 创建位置映射器
            position_mapper = PositionMapper(original_text)
            
            # 解析单词结果
            words_data = data.get('words', [])
            if not words_data and 'word' in data:
                words_data = [data['word']]  # 单个单词的情况
            
            words = []
            for word_data in words_data:
                word_result = self._parse_word_result(word_data, original_text, position_mapper)
                if word_result:
                    words.append(word_result)
            
            # 创建句子结果（对于单词级测试，整个文本作为一个句子）
            if words:
                sentence = SentenceResult(
                    content=original_text,
                    total_score=data.get('total_score', 0.0),
                    integrity_score=data.get('integrity_score'),
                    fluency_score=data.get('fluency_score'),
                    words=words
                )
                sentences.append(sentence)
        
        except Exception as e:
            log_warning(f"解析单词级别结果时发生错误: {str(e)}")
        
        return sentences
    
    def _parse_sentence_level_results(self, data: Dict[str, Any], original_text: str) -> List[SentenceResult]:
        """解析句子级别的结果（读文章、自由说话）
        
        Args:
            data: API数据
            original_text: 原始文本
            
        Returns:
            句子结果列表
        """
        sentences = []
        
        try:
            # 创建位置映射器
            position_mapper = PositionMapper(original_text)
            
            # 解析句子结果
            sentences_data = data.get('sentences', [])
            if not sentences_data and 'sentence' in data:
                sentences_data = [data['sentence']]  # 单个句子的情况
            
            for sentence_data in sentences_data:
                sentence_result = self._parse_sentence_result(sentence_data, original_text, position_mapper)
                if sentence_result:
                    sentences.append(sentence_result)
            
            # 如果没有句子数据，尝试解析为单个句子
            if not sentences and 'words' in data:
                sentence = SentenceResult(
                    content=original_text,
                    total_score=data.get('total_score', 0.0),
                    integrity_score=data.get('integrity_score'),
                    fluency_score=data.get('fluency_score')
                )
                
                # 解析单词
                words_data = data.get('words', [])
                for word_data in words_data:
                    word_result = self._parse_word_result(word_data, original_text, position_mapper)
                    if word_result:
                        sentence.words.append(word_result)
                
                sentences.append(sentence)
        
        except Exception as e:
            log_warning(f"解析句子级别结果时发生错误: {str(e)}")
        
        return sentences
    
    def _parse_sentence_result(self, sentence_data: Dict[str, Any], 
                             original_text: str, position_mapper: PositionMapper) -> Optional[SentenceResult]:
        """解析单个句子结果
        
        Args:
            sentence_data: 句子数据
            original_text: 原始文本
            position_mapper: 位置映射器
            
        Returns:
            句子结果对象
        """
        try:
            # 提取句子内容
            content = sentence_data.get('content', '')
            if not content:
                # 尝试从位置信息提取内容
                beg_pos = sentence_data.get('beg_pos', 0)
                end_pos = sentence_data.get('end_pos', len(original_text.encode('utf-8')))
                content = TextProcessor.extract_text_segment(original_text, beg_pos, end_pos)
            
            sentence = SentenceResult(
                content=content,
                total_score=self._safe_float(sentence_data.get('total_score', 0.0)),
                integrity_score=self._safe_float(sentence_data.get('integrity_score')),
                fluency_score=self._safe_float(sentence_data.get('fluency_score'))
            )
            
            # 解析单词
            words_data = sentence_data.get('words', [])
            for word_data in words_data:
                word_result = self._parse_word_result(word_data, original_text, position_mapper)
                if word_result:
                    sentence.words.append(word_result)
            
            # 解析句子级错误
            sentence.errors = self._parse_errors(sentence_data, original_text)
            
            return sentence
            
        except Exception as e:
            log_warning(f"解析句子结果时发生错误: {str(e)}")
            return None
    
    def _parse_word_result(self, word_data: Dict[str, Any], 
                          original_text: str, position_mapper: PositionMapper) -> Optional[WordResult]:
        """解析单个单词结果
        
        Args:
            word_data: 单词数据
            original_text: 原始文本
            position_mapper: 位置映射器
            
        Returns:
            单词结果对象
        """
        try:
            # 提取位置信息
            beg_pos = word_data.get('beg_pos', 0)
            end_pos = word_data.get('end_pos', beg_pos + 1)
            
            # 提取内容
            content = word_data.get('content', '')
            if not content:
                content = TextProcessor.extract_text_segment(original_text, beg_pos, end_pos)
            
            word = WordResult(
                content=content,
                begin_pos=beg_pos,
                end_pos=end_pos,
                total_score=self._safe_float(word_data.get('total_score', 0.0)),
                phone_score=self._safe_float(word_data.get('phone_score')),
                tone_score=self._safe_float(word_data.get('tone_score'))
            )
            
            # 解析错误
            word.errors = self._parse_errors(word_data, original_text)
            
            return word
            
        except Exception as e:
            log_warning(f"解析单词结果时发生错误: {str(e)}")
            return None
    
    def _parse_errors(self, item_data: Dict[str, Any], original_text: str) -> List[ErrorInfo]:
        """解析错误信息
        
        Args:
            item_data: 项目数据（单词或句子）
            original_text: 原始文本
            
        Returns:
            错误信息列表
        """
        errors = []
        
        try:
            # 从不同字段解析错误
            error_fields = ['errors', 'error', 'details']
            
            for field in error_fields:
                if field in item_data:
                    error_data = item_data[field]
                    if isinstance(error_data, list):
                        for error_item in error_data:
                            error_info = self._parse_single_error(error_item, original_text)
                            if error_info:
                                errors.append(error_info)
                    elif isinstance(error_data, dict):
                        error_info = self._parse_single_error(error_data, original_text)
                        if error_info:
                            errors.append(error_info)
            
            # 基于分数推断错误
            total_score = self._safe_float(item_data.get('total_score', 100.0))
            if total_score < 80:  # 分数较低时推断可能的错误类型
                inferred_errors = self._infer_errors_from_score(item_data, original_text, total_score)
                errors.extend(inferred_errors)
        
        except Exception as e:
            log_warning(f"解析错误信息时发生错误: {str(e)}")
        
        return errors
    
    def _parse_single_error(self, error_data: Dict[str, Any], original_text: str) -> Optional[ErrorInfo]:
        """解析单个错误信息
        
        Args:
            error_data: 错误数据
            original_text: 原始文本
            
        Returns:
            错误信息对象
        """
        try:
            # 确定错误类型
            error_type_str = error_data.get('type', error_data.get('error_type', 'unknown'))
            error_type = self.error_type_mapping.get(error_type_str, ErrorType.INITIAL_CONSONANT)
            
            # 解析位置
            beg_pos = error_data.get('beg_pos', 0)
            end_pos = error_data.get('end_pos', beg_pos + 1)
            char_begin, char_end = byte_to_char_pos(original_text, beg_pos, end_pos)
            
            position = ErrorPosition(
                begin_pos=beg_pos,
                end_pos=end_pos,
                char_begin=char_begin,
                char_end=char_end
            )
            
            # 确定严重程度
            score = self._safe_float(error_data.get('score', 0.0))
            severity = self._determine_severity(score)
            
            error_info = ErrorInfo(
                error_type=error_type,
                position=position,
                severity=severity,
                score=score,
                expected_content=error_data.get('expected'),
                actual_content=error_data.get('actual'),
                suggestion=error_data.get('suggestion'),
                confidence=self._safe_float(error_data.get('confidence'))
            )
            
            return error_info
            
        except Exception as e:
            log_warning(f"解析单个错误时发生错误: {str(e)}")
            return None
    
    def _infer_errors_from_score(self, item_data: Dict[str, Any], 
                               original_text: str, total_score: float) -> List[ErrorInfo]:
        """基于分数推断错误类型
        
        Args:
            item_data: 项目数据
            original_text: 原始文本
            total_score: 总分
            
        Returns:
            推断的错误列表
        """
        errors = []
        
        try:
            beg_pos = item_data.get('beg_pos', 0)
            end_pos = item_data.get('end_pos', beg_pos + 1)
            char_begin, char_end = byte_to_char_pos(original_text, beg_pos, end_pos)
            
            position = ErrorPosition(
                begin_pos=beg_pos,
                end_pos=end_pos,
                char_begin=char_begin,
                char_end=char_end
            )
            
            severity = self._determine_severity(total_score)
            
            # 根据各维度分数推断错误类型
            phone_score = self._safe_float(item_data.get('phone_score'))
            tone_score = self._safe_float(item_data.get('tone_score'))
            fluency_score = self._safe_float(item_data.get('fluency_score'))
            
            if phone_score and phone_score < 70:
                errors.append(ErrorInfo(
                    error_type=ErrorType.INITIAL_CONSONANT,
                    position=position,
                    severity=severity,
                    score=phone_score
                ))
            
            if tone_score and tone_score < 70:
                errors.append(ErrorInfo(
                    error_type=ErrorType.TONE,
                    position=position,
                    severity=severity,
                    score=tone_score
                ))
            
            if fluency_score and fluency_score < 70:
                errors.append(ErrorInfo(
                    error_type=ErrorType.FLUENCY,
                    position=position,
                    severity=severity,
                    score=fluency_score
                ))
        
        except Exception as e:
            log_warning(f"推断错误类型时发生错误: {str(e)}")
        
        return errors
    
    def _parse_global_errors(self, data: Dict[str, Any], original_text: str) -> List[ErrorInfo]:
        """解析全局错误
        
        Args:
            data: API数据
            original_text: 原始文本
            
        Returns:
            全局错误列表
        """
        errors = []
        
        try:
            global_errors_data = data.get('global_errors', [])
            for error_data in global_errors_data:
                error_info = self._parse_single_error(error_data, original_text)
                if error_info:
                    errors.append(error_info)
        
        except Exception as e:
            log_warning(f"解析全局错误时发生错误: {str(e)}")
        
        return errors
    
    def _determine_severity(self, score: float) -> ErrorSeverity:
        """根据分数确定错误严重程度
        
        Args:
            score: 分数
            
        Returns:
            错误严重程度
        """
        if score < 30:
            return ErrorSeverity.CRITICAL
        elif score < 60:
            return ErrorSeverity.MAJOR
        elif score < 80:
            return ErrorSeverity.MINOR
        else:
            return ErrorSeverity.WARNING
    
    def _safe_float(self, value: Any) -> Optional[float]:
        """安全转换为浮点数
        
        Args:
            value: 要转换的值
            
        Returns:
            浮点数或None
        """
        if value is None:
            return None
        
        try:
            return float(value)
        except (ValueError, TypeError):
            return None


# 便捷函数
def parse_api_response(api_response: Dict[str, Any], request_id: str, 
                      test_type: TestType, language: Language, 
                      original_text: str, **kwargs) -> EvaluationResult:
    """解析API响应的便捷函数"""
    parser = ResultParser()
    return parser.parse_evaluation_result(
        api_response, request_id, test_type, language, original_text, **kwargs
    )
