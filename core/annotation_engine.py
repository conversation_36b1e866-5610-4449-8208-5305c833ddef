"""
错误标注引擎模块

实现文本错误标注和可视化功能
"""

from typing import List, Dict, Any, Optional, Tuple
from PyQt6.QtWidgets import QTextEdit, QToolTip
from PyQt6.QtGui import QTextCursor, QTextCharFormat, QTextDocument
from PyQt6.QtCore import Qt, QPoint

from .data_models import EvaluationResult, ErrorInfo, WordResult, SentenceResult
from utils.constants import ErrorType
from utils.style_utils import StyleManager, get_error_color, create_error_format
from utils.text_utils import TextProcessor, byte_to_char_pos
from utils.exception_handler import log_info, log_warning, log_error


class AnnotationEngine:
    """错误标注引擎
    
    负责在文本中标注错误，提供可视化的错误显示
    """
    
    def __init__(self):
        self.style_manager = StyleManager()
        self.annotation_data = {}  # 存储标注数据
    
    def annotate_text(self, text_edit: QTextEdit, evaluation_result: EvaluationResult) -> bool:
        """在文本编辑器中标注错误
        
        Args:
            text_edit: 文本编辑器组件
            evaluation_result: 评测结果
            
        Returns:
            是否标注成功
        """
        try:
            log_info(f"开始标注文本错误: {evaluation_result.request_id}")
            
            # 清除之前的格式
            self._clear_annotations(text_edit)
            
            # 设置原始文本
            text_edit.setPlainText(evaluation_result.original_text)
            
            # 收集所有错误信息
            all_errors = self._collect_all_errors(evaluation_result)
            
            # 按位置排序错误
            sorted_errors = sorted(all_errors, key=lambda x: x.position.begin_pos)
            
            # 应用标注
            for error in sorted_errors:
                self._apply_error_annotation(text_edit, error, evaluation_result.original_text)
            
            # 存储标注数据用于交互
            self.annotation_data[id(text_edit)] = {
                'evaluation_result': evaluation_result,
                'errors': all_errors
            }
            
            log_info(f"文本错误标注完成: 共标注 {len(sorted_errors)} 个错误")
            return True
            
        except Exception as e:
            log_error(f"标注文本错误失败: {str(e)}")
            return False
    
    def _clear_annotations(self, text_edit: QTextEdit):
        """清除文本编辑器中的所有标注
        
        Args:
            text_edit: 文本编辑器
        """
        cursor = text_edit.textCursor()
        cursor.select(QTextCursor.SelectionType.Document)
        
        # 创建默认格式
        default_format = QTextCharFormat()
        cursor.setCharFormat(default_format)
        
        # 清除选择
        cursor.clearSelection()
        text_edit.setTextCursor(cursor)
    
    def _collect_all_errors(self, evaluation_result: EvaluationResult) -> List[ErrorInfo]:
        """收集评测结果中的所有错误
        
        Args:
            evaluation_result: 评测结果
            
        Returns:
            错误信息列表
        """
        all_errors = []
        
        # 收集全局错误
        all_errors.extend(evaluation_result.global_errors)
        
        # 收集句子和单词错误
        for sentence in evaluation_result.sentences:
            # 句子级错误
            all_errors.extend(sentence.errors)
            
            # 单词级错误
            for word in sentence.words:
                all_errors.extend(word.errors)
        
        return all_errors
    
    def _apply_error_annotation(self, text_edit: QTextEdit, error: ErrorInfo, original_text: str):
        """应用单个错误的标注
        
        Args:
            text_edit: 文本编辑器
            error: 错误信息
            original_text: 原始文本
        """
        try:
            # 获取字符位置
            char_start = error.position.char_begin
            char_end = error.position.char_end
            
            # 如果没有字符位置，从字节位置计算
            if char_start is None or char_end is None:
                char_start, char_end = byte_to_char_pos(
                    original_text, 
                    error.position.begin_pos, 
                    error.position.end_pos
                )
            
            # 验证位置有效性
            if char_start < 0 or char_end > len(original_text) or char_start >= char_end:
                log_warning(f"错误位置无效: {char_start}-{char_end}")
                return
            
            # 创建文本光标
            cursor = text_edit.textCursor()
            cursor.setPosition(char_start)
            cursor.setPosition(char_end, QTextCursor.MoveMode.KeepAnchor)
            
            # 获取错误类型对应的格式
            error_format = create_error_format(error.error_type)
            
            # 应用格式
            cursor.setCharFormat(error_format)
            
        except Exception as e:
            log_warning(f"应用错误标注失败: {str(e)}")
    
    def get_error_at_position(self, text_edit: QTextEdit, position: int) -> Optional[ErrorInfo]:
        """获取指定位置的错误信息
        
        Args:
            text_edit: 文本编辑器
            position: 字符位置
            
        Returns:
            错误信息或None
        """
        annotation_data = self.annotation_data.get(id(text_edit))
        if not annotation_data:
            return None
        
        errors = annotation_data['errors']
        
        for error in errors:
            char_start = error.position.char_begin
            char_end = error.position.char_end
            
            if char_start is not None and char_end is not None:
                if char_start <= position < char_end:
                    return error
        
        return None
    
    def show_error_tooltip(self, text_edit: QTextEdit, position: QPoint, cursor_position: int):
        """显示错误工具提示
        
        Args:
            text_edit: 文本编辑器
            position: 鼠标位置
            cursor_position: 光标位置
        """
        error = self.get_error_at_position(text_edit, cursor_position)
        if error:
            tooltip_text = self._create_error_tooltip_text(error)
            QToolTip.showText(position, tooltip_text, text_edit)
    
    def _create_error_tooltip_text(self, error: ErrorInfo) -> str:
        """创建错误工具提示文本
        
        Args:
            error: 错误信息
            
        Returns:
            工具提示文本
        """
        lines = []
        
        # 错误类型
        error_type_names = {
            ErrorType.INITIAL_CONSONANT: "声母错误",
            ErrorType.FINAL_SOUND: "韵母错误",
            ErrorType.TONE: "声调错误",
            ErrorType.FLUENCY: "流畅性问题",
            ErrorType.MISSING: "漏读"
        }
        
        error_name = error_type_names.get(error.error_type, "未知错误")
        lines.append(f"错误类型: {error_name}")
        
        # 严重程度
        severity_names = {
            "critical": "严重",
            "major": "主要",
            "minor": "轻微",
            "warning": "警告"
        }
        severity_name = severity_names.get(error.severity.value, "未知")
        lines.append(f"严重程度: {severity_name}")
        
        # 分数
        if error.score is not None:
            lines.append(f"得分: {error.score:.1f}")
        
        # 期望内容
        if error.expected_content:
            lines.append(f"期望: {error.expected_content}")
        
        # 实际内容
        if error.actual_content:
            lines.append(f"实际: {error.actual_content}")
        
        # 建议
        if error.suggestion:
            lines.append(f"建议: {error.suggestion}")
        
        # 置信度
        if error.confidence is not None:
            lines.append(f"置信度: {error.confidence:.1%}")
        
        return "\n".join(lines)
    
    def create_error_legend(self) -> Dict[ErrorType, Dict[str, str]]:
        """创建错误类型图例
        
        Returns:
            错误类型图例字典
        """
        legend = {}
        
        error_type_names = {
            ErrorType.INITIAL_CONSONANT: "声母错误",
            ErrorType.FINAL_SOUND: "韵母错误", 
            ErrorType.TONE: "声调错误",
            ErrorType.FLUENCY: "流畅性问题",
            ErrorType.MISSING: "漏读"
        }
        
        for error_type, name in error_type_names.items():
            legend[error_type] = {
                'name': name,
                'color': get_error_color(error_type),
                'description': self._get_error_description(error_type)
            }
        
        return legend
    
    def _get_error_description(self, error_type: ErrorType) -> str:
        """获取错误类型描述
        
        Args:
            error_type: 错误类型
            
        Returns:
            错误描述
        """
        descriptions = {
            ErrorType.INITIAL_CONSONANT: "声母发音不准确，如b/p、d/t、g/k等混淆",
            ErrorType.FINAL_SOUND: "韵母发音不准确，如an/ang、en/eng等混淆",
            ErrorType.TONE: "声调不准确，如一声、二声、三声、四声混淆",
            ErrorType.FLUENCY: "语音流畅性问题，如停顿、重复、语速等",
            ErrorType.MISSING: "漏读或跳读，未发出相应音节"
        }
        
        return descriptions.get(error_type, "未知错误类型")
    
    def get_annotation_statistics(self, text_edit: QTextEdit) -> Dict[str, Any]:
        """获取标注统计信息
        
        Args:
            text_edit: 文本编辑器
            
        Returns:
            统计信息字典
        """
        annotation_data = self.annotation_data.get(id(text_edit))
        if not annotation_data:
            return {}
        
        evaluation_result = annotation_data['evaluation_result']
        errors = annotation_data['errors']
        
        # 统计各类错误数量
        error_counts = {}
        for error_type in ErrorType:
            error_counts[error_type.value] = sum(
                1 for error in errors if error.error_type == error_type
            )
        
        # 统计严重程度
        severity_counts = {}
        for error in errors:
            severity = error.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        return {
            'total_errors': len(errors),
            'error_counts': error_counts,
            'severity_counts': severity_counts,
            'total_score': evaluation_result.scores.total_score,
            'accuracy_rate': evaluation_result.get_overall_accuracy()
        }
    
    def export_annotations(self, text_edit: QTextEdit, format: str = "html") -> str:
        """导出标注结果
        
        Args:
            text_edit: 文本编辑器
            format: 导出格式 ("html", "text")
            
        Returns:
            导出的内容
        """
        if format == "html":
            return text_edit.toHtml()
        else:
            return text_edit.toPlainText()
    
    def clear_annotation_data(self, text_edit: QTextEdit):
        """清除标注数据
        
        Args:
            text_edit: 文本编辑器
        """
        widget_id = id(text_edit)
        if widget_id in self.annotation_data:
            del self.annotation_data[widget_id]


# 便捷函数
def annotate_evaluation_result(text_edit: QTextEdit, evaluation_result: EvaluationResult) -> bool:
    """标注评测结果的便捷函数"""
    engine = AnnotationEngine()
    return engine.annotate_text(text_edit, evaluation_result)


def create_annotation_engine() -> AnnotationEngine:
    """创建标注引擎的便捷函数"""
    return AnnotationEngine()
