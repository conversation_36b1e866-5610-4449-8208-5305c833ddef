"""
数据模型定义模块

定义评测结果、错误信息等数据结构
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Union
from enum import Enum
from datetime import datetime

from utils.constants import TestType, Language, ErrorType


class ScoreLevel(Enum):
    """评分等级枚举"""
    EXCELLENT = "excellent"  # 优秀 (90-100)
    GOOD = "good"           # 良好 (80-89)
    FAIR = "fair"           # 及格 (70-79)
    POOR = "poor"           # 不及格 (0-69)


class ErrorSeverity(Enum):
    """错误严重程度枚举"""
    CRITICAL = "critical"   # 严重错误
    MAJOR = "major"        # 主要错误
    MINOR = "minor"        # 轻微错误
    WARNING = "warning"    # 警告


@dataclass
class ScoreInfo:
    """评分信息数据类"""
    total_score: float = 0.0                    # 总分
    phone_score: Optional[float] = None         # 声韵分（声母+韵母）
    tone_score: Optional[float] = None          # 调型分（声调）
    integrity_score: Optional[float] = None     # 完整度分
    fluency_score: Optional[float] = None       # 流畅度分
    
    # 细分评分
    initial_consonant_score: Optional[float] = None  # 声母分
    final_sound_score: Optional[float] = None        # 韵母分
    
    def get_score_level(self, score: float) -> ScoreLevel:
        """获取评分等级"""
        if score >= 90:
            return ScoreLevel.EXCELLENT
        elif score >= 80:
            return ScoreLevel.GOOD
        elif score >= 70:
            return ScoreLevel.FAIR
        else:
            return ScoreLevel.POOR
    
    def get_total_level(self) -> ScoreLevel:
        """获取总分等级"""
        return self.get_score_level(self.total_score)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'total_score': self.total_score,
            'phone_score': self.phone_score,
            'tone_score': self.tone_score,
            'integrity_score': self.integrity_score,
            'fluency_score': self.fluency_score,
            'initial_consonant_score': self.initial_consonant_score,
            'final_sound_score': self.final_sound_score,
            'total_level': self.get_total_level().value
        }


@dataclass
class ErrorPosition:
    """错误位置信息数据类"""
    begin_pos: int                              # 开始位置（字节）
    end_pos: int                                # 结束位置（字节）
    char_begin: Optional[int] = None            # 字符开始位置
    char_end: Optional[int] = None              # 字符结束位置
    word_index: Optional[int] = None            # 词索引
    syllable_index: Optional[int] = None        # 音节索引
    
    def get_length(self) -> int:
        """获取错误长度（字节）"""
        return self.end_pos - self.begin_pos
    
    def get_char_length(self) -> int:
        """获取错误长度（字符）"""
        if self.char_begin is not None and self.char_end is not None:
            return self.char_end - self.char_begin
        return 0


@dataclass
class ErrorInfo:
    """错误信息数据类"""
    error_type: ErrorType                       # 错误类型
    position: ErrorPosition                     # 错误位置
    severity: ErrorSeverity                     # 严重程度
    score: Optional[float] = None               # 该位置的评分
    expected_content: Optional[str] = None      # 期望内容
    actual_content: Optional[str] = None        # 实际内容
    suggestion: Optional[str] = None            # 改进建议
    confidence: Optional[float] = None          # 置信度
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'error_type': self.error_type.value,
            'position': {
                'begin_pos': self.position.begin_pos,
                'end_pos': self.position.end_pos,
                'char_begin': self.position.char_begin,
                'char_end': self.position.char_end,
                'word_index': self.position.word_index,
                'syllable_index': self.position.syllable_index
            },
            'severity': self.severity.value,
            'score': self.score,
            'expected_content': self.expected_content,
            'actual_content': self.actual_content,
            'suggestion': self.suggestion,
            'confidence': self.confidence
        }


@dataclass
class WordResult:
    """单词/字符评测结果数据类"""
    content: str                                # 内容
    begin_pos: int                              # 开始位置
    end_pos: int                                # 结束位置
    total_score: float                          # 总分
    phone_score: Optional[float] = None         # 声韵分
    tone_score: Optional[float] = None          # 调型分
    errors: List[ErrorInfo] = field(default_factory=list)  # 错误列表
    
    def has_errors(self) -> bool:
        """是否有错误"""
        return len(self.errors) > 0
    
    def get_error_count_by_type(self, error_type: ErrorType) -> int:
        """获取指定类型的错误数量"""
        return sum(1 for error in self.errors if error.error_type == error_type)
    
    def get_worst_error_severity(self) -> Optional[ErrorSeverity]:
        """获取最严重的错误等级"""
        if not self.errors:
            return None
        
        severity_order = [ErrorSeverity.CRITICAL, ErrorSeverity.MAJOR, 
                         ErrorSeverity.MINOR, ErrorSeverity.WARNING]
        
        for severity in severity_order:
            if any(error.severity == severity for error in self.errors):
                return severity
        
        return None


@dataclass
class SentenceResult:
    """句子评测结果数据类"""
    content: str                                # 句子内容
    total_score: float                          # 总分
    integrity_score: Optional[float] = None     # 完整度分
    fluency_score: Optional[float] = None       # 流畅度分
    words: List[WordResult] = field(default_factory=list)  # 单词结果列表
    errors: List[ErrorInfo] = field(default_factory=list)  # 句子级错误
    
    def get_word_count(self) -> int:
        """获取单词数量"""
        return len(self.words)
    
    def get_error_word_count(self) -> int:
        """获取有错误的单词数量"""
        return sum(1 for word in self.words if word.has_errors())
    
    def get_accuracy_rate(self) -> float:
        """获取准确率"""
        if not self.words:
            return 0.0
        return (self.get_word_count() - self.get_error_word_count()) / self.get_word_count()


@dataclass
class EvaluationResult:
    """完整评测结果数据类"""
    request_id: str                             # 请求ID
    test_type: TestType                         # 测试类型
    language: Language                          # 语言类型
    original_text: str                          # 原始文本
    audio_duration: Optional[float] = None      # 音频时长
    
    # 评分信息
    scores: ScoreInfo = field(default_factory=ScoreInfo)
    
    # 详细结果
    sentences: List[SentenceResult] = field(default_factory=list)
    global_errors: List[ErrorInfo] = field(default_factory=list)
    
    # 元数据
    created_at: datetime = field(default_factory=datetime.now)
    processing_time: Optional[float] = None     # 处理时间（秒）
    api_response_raw: Optional[Dict[str, Any]] = None  # 原始API响应
    
    def get_total_word_count(self) -> int:
        """获取总单词数量"""
        return sum(sentence.get_word_count() for sentence in self.sentences)
    
    def get_total_error_count(self) -> int:
        """获取总错误数量"""
        word_errors = sum(len(word.errors) for sentence in self.sentences for word in sentence.words)
        sentence_errors = sum(len(sentence.errors) for sentence in self.sentences)
        global_errors = len(self.global_errors)
        return word_errors + sentence_errors + global_errors
    
    def get_error_count_by_type(self, error_type: ErrorType) -> int:
        """获取指定类型的错误总数"""
        count = 0
        
        # 单词级错误
        for sentence in self.sentences:
            for word in sentence.words:
                count += word.get_error_count_by_type(error_type)
        
        # 句子级错误
        for sentence in self.sentences:
            count += sum(1 for error in sentence.errors if error.error_type == error_type)
        
        # 全局错误
        count += sum(1 for error in self.global_errors if error.error_type == error_type)
        
        return count
    
    def get_overall_accuracy(self) -> float:
        """获取整体准确率"""
        if not self.sentences:
            return 0.0
        
        total_words = self.get_total_word_count()
        if total_words == 0:
            return 0.0
        
        error_words = sum(sentence.get_error_word_count() for sentence in self.sentences)
        return (total_words - error_words) / total_words
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取统计摘要"""
        return {
            'total_score': self.scores.total_score,
            'score_level': self.scores.get_total_level().value,
            'total_words': self.get_total_word_count(),
            'total_errors': self.get_total_error_count(),
            'accuracy_rate': self.get_overall_accuracy(),
            'error_breakdown': {
                'initial_consonant': self.get_error_count_by_type(ErrorType.INITIAL_CONSONANT),
                'final_sound': self.get_error_count_by_type(ErrorType.FINAL_SOUND),
                'tone': self.get_error_count_by_type(ErrorType.TONE),
                'fluency': self.get_error_count_by_type(ErrorType.FLUENCY),
                'missing': self.get_error_count_by_type(ErrorType.MISSING)
            },
            'processing_time': self.processing_time,
            'audio_duration': self.audio_duration
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'request_id': self.request_id,
            'test_type': self.test_type.value,
            'language': self.language.value,
            'original_text': self.original_text,
            'audio_duration': self.audio_duration,
            'scores': self.scores.to_dict(),
            'sentences': [
                {
                    'content': sentence.content,
                    'total_score': sentence.total_score,
                    'integrity_score': sentence.integrity_score,
                    'fluency_score': sentence.fluency_score,
                    'words': [
                        {
                            'content': word.content,
                            'begin_pos': word.begin_pos,
                            'end_pos': word.end_pos,
                            'total_score': word.total_score,
                            'phone_score': word.phone_score,
                            'tone_score': word.tone_score,
                            'errors': [error.to_dict() for error in word.errors]
                        }
                        for word in sentence.words
                    ],
                    'errors': [error.to_dict() for error in sentence.errors]
                }
                for sentence in self.sentences
            ],
            'global_errors': [error.to_dict() for error in self.global_errors],
            'summary_stats': self.get_summary_stats(),
            'created_at': self.created_at.isoformat(),
            'processing_time': self.processing_time
        }
