"""
核心业务逻辑模块

包含语音评测、数据处理、API调用等核心功能
"""

# 版本信息
__version__ = "1.0.0"
__author__ = "开发团队"

# 导出主要的核心组件
from .audio_processor import AudioProcessor, AudioProcessorWorker, BatchAudioProcessor
from .audio_processor import process_audio_file, encode_audio_to_base64
from .api_client import APIClient, APIError, APIRetryManager
from .api_worker import APIWorker, BatchAPIWorker, create_api_worker, create_batch_api_worker
from .websocket_api_client import WebSocketAPIClient, WebSocketAPIWorker, create_websocket_api_client, create_websocket_api_worker
from .result_parser import ResultParser, parse_api_response
from .data_models import (
    EvaluationResult, ScoreInfo, SentenceResult, WordResult,
    ErrorInfo, ErrorPosition, ScoreLevel, ErrorSeverity
)
from .annotation_engine import AnnotationEngine, annotate_evaluation_result, create_annotation_engine

__all__ = [
    "AudioProcessor",
    "AudioProcessorWorker",
    "BatchAudioProcessor",
    "process_audio_file",
    "encode_audio_to_base64",
    "APIClient",
    "APIError",
    "APIRetryManager",
    "APIWorker",
    "BatchAPIWorker",
    "create_api_worker",
    "create_batch_api_worker",
    "WebSocketAPIClient",
    "WebSocketAPIWorker",
    "create_websocket_api_client",
    "create_websocket_api_worker",
    "ResultParser",
    "parse_api_response",
    "EvaluationResult",
    "ScoreInfo",
    "SentenceResult",
    "WordResult",
    "ErrorInfo",
    "ErrorPosition",
    "ScoreLevel",
    "ErrorSeverity",
    "AnnotationEngine",
    "annotate_evaluation_result",
    "create_annotation_engine"
]
