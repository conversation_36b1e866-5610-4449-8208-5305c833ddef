"""
音频处理核心模块

提供音频文件的格式转换、质量检测、预处理等功能
"""

import base64
from pathlib import Path
from typing import Optional, Dict, Any, Tuple
from PyQt6.QtCore import QObject, pyqtSignal, QThread

from utils.constants import (
    TARGET_SAMPLE_RATE, TARGET_CHANNELS, TARGET_BIT_DEPTH,
    SUPPORTED_AUDIO_FORMATS
)
from utils.file_utils import FileUtils, file_history
from utils.validators import AudioValidator
from utils.exception_handler import log_info, log_error, log_warning


class AudioProcessor(QObject):
    """音频处理器
    
    负责音频文件的格式转换、质量检测和预处理
    """
    
    # 处理进度信号
    progress_updated = pyqtSignal(int)  # 进度百分比
    status_updated = pyqtSignal(str)    # 状态信息
    processing_finished = pyqtSignal(bool, str, dict)  # 是否成功, 消息, 结果数据
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.temp_dir = FileUtils.get_temp_directory()
    
    def process_audio_file(self, file_path: Path, target_format: str = "wav") -> Dict[str, Any]:
        """处理音频文件
        
        Args:
            file_path: 音频文件路径
            target_format: 目标格式
            
        Returns:
            处理结果字典
        """
        try:
            self.status_updated.emit("开始处理音频文件...")
            self.progress_updated.emit(0)
            
            # 验证音频文件
            is_valid, error, properties = AudioValidator.validate_audio_properties(file_path)
            if not is_valid:
                return {
                    "success": False,
                    "error": error,
                    "original_file": str(file_path)
                }
            
            self.progress_updated.emit(20)
            self.status_updated.emit("验证音频属性...")
            
            # 检查是否需要转换
            needs_conversion = self._needs_conversion(properties, file_path.suffix.lower())
            
            if needs_conversion:
                self.status_updated.emit("转换音频格式...")
                converted_path = self._convert_audio(file_path, target_format, properties)
                if not converted_path:
                    return {
                        "success": False,
                        "error": "音频格式转换失败",
                        "original_file": str(file_path)
                    }
                processed_file = converted_path
            else:
                # 不需要转换，复制到临时目录
                processed_file = self._copy_to_temp(file_path)
            
            self.progress_updated.emit(80)
            self.status_updated.emit("检查音频质量...")
            
            # 检查音频质量
            quality_info = AudioValidator.check_audio_quality(processed_file)
            
            self.progress_updated.emit(100)
            self.status_updated.emit("音频处理完成")
            
            # 生成Base64编码的音频数据
            base64_data = self.encode_audio_to_base64(processed_file)
            if not base64_data:
                raise Exception("音频Base64编码失败")

            # 记录处理历史
            file_history.add_record(
                file_path,
                "audio_processed",
                {
                    "converted": needs_conversion,
                    "target_format": target_format,
                    "quality_score": quality_info.get("quality_score", 0)
                }
            )

            result = {
                "success": True,
                "original_file": str(file_path),
                "processed_file": str(processed_file),
                "base64_data": base64_data,
                "converted": needs_conversion,
                "properties": properties,
                "quality_info": quality_info,
                "target_format": target_format
            }
            
            # 不在这里发送信号，由AudioProcessorWorker统一发送
            return result
            
        except Exception as e:
            error_msg = f"处理音频文件时发生错误: {str(e)}"
            # 不在这里发送信号，由AudioProcessorWorker统一发送
            return {
                "success": False,
                "error": error_msg,
                "original_file": str(file_path)
            }
    
    def _needs_conversion(self, properties: Dict[str, Any], file_ext: str) -> bool:
        """检查是否需要格式转换
        
        Args:
            properties: 音频属性
            file_ext: 文件扩展名
            
        Returns:
            是否需要转换
        """
        # 检查格式
        if file_ext not in ['.wav', '.pcm']:
            return True
        
        # 检查采样率
        if properties.get("sample_rate") != TARGET_SAMPLE_RATE:
            return True
        
        # 检查声道数
        if properties.get("channels") != TARGET_CHANNELS:
            return True
        
        # 检查位深度
        if properties.get("sample_width") != TARGET_BIT_DEPTH // 8:
            return True
        
        return False
    
    def _convert_audio(self, source_path: Path, target_format: str, properties: Dict[str, Any]) -> Optional[Path]:
        """转换音频格式
        
        Args:
            source_path: 源文件路径
            target_format: 目标格式
            properties: 音频属性
            
        Returns:
            转换后的文件路径
        """
        try:
            from pydub import AudioSegment
            
            self.progress_updated.emit(30)
            
            # 加载音频文件
            audio = AudioSegment.from_file(str(source_path))
            
            self.progress_updated.emit(50)
            
            # 转换参数
            audio = audio.set_frame_rate(TARGET_SAMPLE_RATE)
            audio = audio.set_channels(TARGET_CHANNELS)
            audio = audio.set_sample_width(TARGET_BIT_DEPTH // 8)
            
            self.progress_updated.emit(70)
            
            # 生成输出文件路径
            output_path = self.temp_dir / f"converted_{source_path.stem}.{target_format}"
            
            # 导出音频
            audio.export(str(output_path), format=target_format)
            
            return output_path
            
        except ImportError:
            log_error("错误：缺少pydub库，无法进行音频转换")
            return None
        except Exception as e:
            log_error(f"音频转换失败: {e}")
            return None
    
    def _copy_to_temp(self, source_path: Path) -> Path:
        """复制文件到临时目录
        
        Args:
            source_path: 源文件路径
            
        Returns:
            临时文件路径
        """
        import shutil
        
        temp_path = self.temp_dir / f"temp_{source_path.name}"
        shutil.copy2(source_path, temp_path)
        return temp_path
    
    def encode_audio_to_base64(self, file_path: Path) -> Optional[str]:
        """将音频文件编码为Base64
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Base64编码的音频数据
        """
        try:
            with open(file_path, 'rb') as f:
                audio_data = f.read()
            
            return base64.b64encode(audio_data).decode('utf-8')
            
        except Exception as e:
            log_error(f"音频Base64编码失败: {e}")
            return None
    
    def get_audio_duration(self, file_path: Path) -> Optional[float]:
        """获取音频时长
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            音频时长（秒）
        """
        try:
            from pydub import AudioSegment
            
            audio = AudioSegment.from_file(str(file_path))
            return len(audio) / 1000.0
            
        except ImportError:
            print("错误：缺少pydub库")
            return None
        except Exception as e:
            print(f"获取音频时长失败: {e}")
            return None


class AudioProcessorWorker(QThread):
    """音频处理工作线程
    
    用于在后台线程中处理音频，避免阻塞UI
    """
    
    # 信号定义
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    processing_finished = pyqtSignal(bool, str, dict)
    
    def __init__(self, file_path: Path, target_format: str = "wav", parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.target_format = target_format
        self.processor = AudioProcessor()
        
        # 连接信号
        self.processor.progress_updated.connect(self.progress_updated)
        self.processor.status_updated.connect(self.status_updated)
        self.processor.processing_finished.connect(self.processing_finished)
    
    def run(self):
        """运行音频处理"""
        try:
            log_info(f"开始处理音频文件: {self.file_path}")
            result = self.processor.process_audio_file(self.file_path, self.target_format)

            if result and result.get("success", False):
                log_info(f"音频处理成功: {self.file_path}")
                self.processing_finished.emit(True, "音频处理成功", result)
            else:
                error_msg = result.get("error", "音频处理失败") if result else "音频处理失败"
                log_error(f"音频处理失败: {self.file_path} - {error_msg}")
                self.processing_finished.emit(False, error_msg, {})

        except Exception as e:
            error_msg = f"音频处理异常: {str(e)}"
            log_error(f"音频处理异常: {self.file_path} - {error_msg}")
            self.processing_finished.emit(False, error_msg, {})


class BatchAudioProcessor(QObject):
    """批量音频处理器"""
    
    # 批量处理信号
    batch_progress_updated = pyqtSignal(int, int)  # 当前索引, 总数
    file_processed = pyqtSignal(str, bool, str)    # 文件名, 是否成功, 消息
    batch_finished = pyqtSignal(int, int)          # 成功数, 失败数
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.processor = AudioProcessor()
    
    def process_files(self, file_paths: list, target_format: str = "wav"):
        """批量处理音频文件
        
        Args:
            file_paths: 文件路径列表
            target_format: 目标格式
        """
        success_count = 0
        failure_count = 0
        
        for i, file_path in enumerate(file_paths):
            self.batch_progress_updated.emit(i + 1, len(file_paths))
            
            result = self.processor.process_audio_file(Path(file_path), target_format)
            
            if result.get("success"):
                success_count += 1
                self.file_processed.emit(file_path, True, "处理成功")
            else:
                failure_count += 1
                error_msg = result.get("error", "未知错误")
                self.file_processed.emit(file_path, False, error_msg)
        
        self.batch_finished.emit(success_count, failure_count)


# 便捷函数
def process_audio_file(file_path: str, target_format: str = "wav") -> Dict[str, Any]:
    """处理音频文件的便捷函数"""
    processor = AudioProcessor()
    return processor.process_audio_file(Path(file_path), target_format)


def encode_audio_to_base64(file_path: str) -> Optional[str]:
    """音频Base64编码的便捷函数"""
    processor = AudioProcessor()
    return processor.encode_audio_to_base64(Path(file_path))
