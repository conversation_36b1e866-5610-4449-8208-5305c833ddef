"""
异步API调用工作线程模块

提供后台API调用功能，避免阻塞UI线程
"""

import time
from typing import Dict, Any, Optional
from PyQt6.QtCore import QThread, pyqtSignal, QMutex, QWaitCondition

from .api_client import APIClient, APIRetryManager
from utils.config_manager import ConfigManager
from utils.constants import TestType, Language
from utils.exception_handler import log_info, log_warning, log_error


class APIWorker(QThread):
    """API调用工作线程
    
    在后台线程中执行API调用，避免阻塞UI
    """
    
    # 工作线程信号
    request_started = pyqtSignal(str)  # 请求ID
    request_progress = pyqtSignal(str, int)  # 请求ID, 进度百分比
    request_completed = pyqtSignal(str, bool, dict)  # 请求ID, 是否成功, 结果数据
    request_failed = pyqtSignal(str, str, int)  # 请求ID, 错误信息, 错误代码
    worker_status_changed = pyqtSignal(str)  # 工作状态变更
    
    def __init__(self, config_manager: Config<PERSON>anager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.api_client = None
        self.retry_manager = APIRetryManager()
        
        # 线程控制
        self.mutex = QMutex()
        self.condition = QWaitCondition()
        self.is_running = False
        self.should_stop = False
        
        # 请求队列
        self.request_queue = []
        self.current_request = None
        
        # 初始化API客户端
        self._init_api_client()
    
    def _init_api_client(self):
        """初始化API客户端"""
        try:
            self.api_client = APIClient(self.config_manager)
            
            # 连接API客户端信号
            self.api_client.request_started.connect(self.request_started)
            self.api_client.request_progress.connect(self.request_progress)
            self.api_client.request_completed.connect(self._on_request_completed)
            self.api_client.request_failed.connect(self._on_request_failed)
            
            log_info("API工作线程初始化成功")
            
        except Exception as e:
            log_error(f"API工作线程初始化失败: {str(e)}")
    
    def add_request(self, audio_data: str, text_content: str, 
                   test_type: TestType, language: Language = Language.CHINESE,
                   request_id: Optional[str] = None, priority: int = 0) -> str:
        """添加API请求到队列
        
        Args:
            audio_data: Base64编码的音频数据
            text_content: 测试文本内容
            test_type: 测试类型
            language: 语言类型
            request_id: 请求ID（可选）
            priority: 优先级（数字越大优先级越高）
            
        Returns:
            请求ID
        """
        if not request_id:
            request_id = f"req_{int(time.time() * 1000)}"
        
        request_data = {
            'request_id': request_id,
            'audio_data': audio_data,
            'text_content': text_content,
            'test_type': test_type,
            'language': language,
            'priority': priority,
            'retry_count': 0,
            'created_at': time.time()
        }
        
        self.mutex.lock()
        try:
            # 按优先级插入队列
            inserted = False
            for i, existing_request in enumerate(self.request_queue):
                if priority > existing_request['priority']:
                    self.request_queue.insert(i, request_data)
                    inserted = True
                    break
            
            if not inserted:
                self.request_queue.append(request_data)
            
            log_info(f"API请求已添加到队列: {request_id}, 队列长度: {len(self.request_queue)}")
            
            # 唤醒工作线程
            self.condition.wakeOne()
            
        finally:
            self.mutex.unlock()
        
        return request_id
    
    def cancel_request(self, request_id: str) -> bool:
        """取消API请求
        
        Args:
            request_id: 请求ID
            
        Returns:
            是否成功取消
        """
        self.mutex.lock()
        try:
            # 从队列中移除请求
            for i, request_data in enumerate(self.request_queue):
                if request_data['request_id'] == request_id:
                    self.request_queue.pop(i)
                    log_info(f"API请求已从队列中取消: {request_id}")
                    return True
            
            # 检查是否是当前正在处理的请求
            if (self.current_request and 
                self.current_request['request_id'] == request_id):
                if self.api_client:
                    self.api_client.cancel_request(request_id)
                log_info(f"当前API请求已取消: {request_id}")
                return True
            
            return False
            
        finally:
            self.mutex.unlock()
    
    def clear_queue(self):
        """清空请求队列"""
        self.mutex.lock()
        try:
            cleared_count = len(self.request_queue)
            self.request_queue.clear()
            log_info(f"API请求队列已清空，清除了 {cleared_count} 个请求")
        finally:
            self.mutex.unlock()
    
    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态
        
        Returns:
            队列状态信息
        """
        self.mutex.lock()
        try:
            return {
                'queue_length': len(self.request_queue),
                'current_request': self.current_request['request_id'] if self.current_request else None,
                'is_running': self.is_running,
                'should_stop': self.should_stop
            }
        finally:
            self.mutex.unlock()
    
    def run(self):
        """线程主循环"""
        self.is_running = True
        self.worker_status_changed.emit("started")
        log_info("API工作线程开始运行")
        
        while not self.should_stop:
            self.mutex.lock()
            
            # 检查队列是否为空
            if not self.request_queue:
                # 等待新请求或停止信号
                self.condition.wait(self.mutex, 5000)  # 5秒超时
                self.mutex.unlock()
                continue
            
            # 获取下一个请求
            self.current_request = self.request_queue.pop(0)
            self.mutex.unlock()
            
            # 处理请求
            self._process_request(self.current_request)
            
            # 清除当前请求
            self.current_request = None
        
        self.is_running = False
        self.worker_status_changed.emit("stopped")
        log_info("API工作线程已停止")
    
    def _process_request(self, request_data: Dict[str, Any]):
        """处理单个API请求
        
        Args:
            request_data: 请求数据
        """
        request_id = request_data['request_id']
        
        try:
            log_info(f"开始处理API请求: {request_id}")
            
            # 检查是否应该停止
            if self.should_stop:
                return
            
            # 调用API
            if self.api_client:
                self.api_client.evaluate_audio(
                    request_data['audio_data'],
                    request_data['text_content'],
                    request_data['test_type'],
                    request_data['language'],
                    request_id
                )
            else:
                raise Exception("API客户端未初始化")
                
        except Exception as e:
            log_error(f"处理API请求失败: {request_id} - {str(e)}")
            
            # 检查是否需要重试
            if self._should_retry_request(request_data, e):
                self._retry_request(request_data)
            else:
                self.request_failed.emit(request_id, str(e), -1)
    
    def _should_retry_request(self, request_data: Dict[str, Any], error: Exception) -> bool:
        """判断是否应该重试请求
        
        Args:
            request_data: 请求数据
            error: 错误信息
            
        Returns:
            是否应该重试
        """
        return self.retry_manager.should_retry(request_data['retry_count'], error)
    
    def _retry_request(self, request_data: Dict[str, Any]):
        """重试API请求
        
        Args:
            request_data: 请求数据
        """
        request_data['retry_count'] += 1
        retry_delay = self.retry_manager.get_retry_delay(request_data['retry_count'])
        
        log_info(f"API请求将在 {retry_delay} 秒后重试: {request_data['request_id']} "
                f"(第 {request_data['retry_count']} 次重试)")
        
        # 等待重试延迟
        self.msleep(int(retry_delay * 1000))
        
        # 重新添加到队列
        self.mutex.lock()
        try:
            self.request_queue.insert(0, request_data)  # 插入到队列前面
        finally:
            self.mutex.unlock()
    
    def _on_request_completed(self, request_id: str, success: bool, result: Dict[str, Any]):
        """API请求完成处理
        
        Args:
            request_id: 请求ID
            success: 是否成功
            result: 结果数据
        """
        log_info(f"API请求完成: {request_id}, 成功: {success}")
        self.request_completed.emit(request_id, success, result)
    
    def _on_request_failed(self, request_id: str, error_message: str, error_code: int):
        """API请求失败处理
        
        Args:
            request_id: 请求ID
            error_message: 错误信息
            error_code: 错误代码
        """
        log_error(f"API请求失败: {request_id}, 错误: {error_message}, 代码: {error_code}")
        
        # 检查当前请求是否需要重试
        if (self.current_request and 
            self.current_request['request_id'] == request_id):
            
            # 创建异常对象用于重试判断
            from .api_client import APIError
            error = APIError(error_message, error_code)
            
            if self._should_retry_request(self.current_request, error):
                self._retry_request(self.current_request)
                return
        
        self.request_failed.emit(request_id, error_message, error_code)
    
    def stop(self):
        """停止工作线程"""
        log_info("正在停止API工作线程...")
        self.should_stop = True
        
        self.mutex.lock()
        self.condition.wakeOne()
        self.mutex.unlock()
        
        # 等待线程结束
        if not self.wait(5000):  # 5秒超时
            log_warning("API工作线程停止超时，强制终止")
            self.terminate()
    
    def cleanup(self):
        """清理资源"""
        if self.api_client:
            self.api_client.cleanup()
        
        self.clear_queue()
        log_info("API工作线程资源已清理")


class BatchAPIWorker(APIWorker):
    """批量API调用工作线程"""
    
    # 批量处理信号
    batch_started = pyqtSignal(str, int)  # 批次ID, 总数
    batch_progress = pyqtSignal(str, int, int)  # 批次ID, 已完成数, 总数
    batch_completed = pyqtSignal(str, int, int)  # 批次ID, 成功数, 失败数
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        super().__init__(config_manager, parent)
        self.batch_requests = {}  # 批次请求跟踪
    
    def add_batch_requests(self, requests: list, batch_id: Optional[str] = None) -> str:
        """添加批量API请求
        
        Args:
            requests: 请求列表
            batch_id: 批次ID（可选）
            
        Returns:
            批次ID
        """
        if not batch_id:
            batch_id = f"batch_{int(time.time() * 1000)}"
        
        # 初始化批次跟踪
        self.batch_requests[batch_id] = {
            'total': len(requests),
            'completed': 0,
            'success': 0,
            'failed': 0,
            'request_ids': []
        }
        
        # 添加所有请求到队列
        for request_data in requests:
            request_id = self.add_request(**request_data)
            self.batch_requests[batch_id]['request_ids'].append(request_id)
        
        # 发送批次开始信号
        self.batch_started.emit(batch_id, len(requests))
        log_info(f"批量API请求已添加: {batch_id}, 总数: {len(requests)}")
        
        return batch_id
    
    def _on_request_completed(self, request_id: str, success: bool, result: Dict[str, Any]):
        """重写请求完成处理，添加批次跟踪"""
        super()._on_request_completed(request_id, success, result)
        self._update_batch_progress(request_id, success)
    
    def _on_request_failed(self, request_id: str, error_message: str, error_code: int):
        """重写请求失败处理，添加批次跟踪"""
        super()._on_request_failed(request_id, error_message, error_code)
        self._update_batch_progress(request_id, False)
    
    def _update_batch_progress(self, request_id: str, success: bool):
        """更新批次进度
        
        Args:
            request_id: 请求ID
            success: 是否成功
        """
        # 查找请求所属的批次
        for batch_id, batch_info in self.batch_requests.items():
            if request_id in batch_info['request_ids']:
                batch_info['completed'] += 1
                
                if success:
                    batch_info['success'] += 1
                else:
                    batch_info['failed'] += 1
                
                # 发送进度更新信号
                self.batch_progress.emit(
                    batch_id, 
                    batch_info['completed'], 
                    batch_info['total']
                )
                
                # 检查批次是否完成
                if batch_info['completed'] >= batch_info['total']:
                    self.batch_completed.emit(
                        batch_id,
                        batch_info['success'],
                        batch_info['failed']
                    )
                    
                    # 清理批次信息
                    del self.batch_requests[batch_id]
                    log_info(f"批量API请求完成: {batch_id}")

                break


# 便捷函数
def create_api_worker(config_manager: ConfigManager) -> APIWorker:
    """创建API工作线程的便捷函数"""
    return APIWorker(config_manager)


def create_batch_api_worker(config_manager: ConfigManager) -> BatchAPIWorker:
    """创建批量API工作线程的便捷函数"""
    return BatchAPIWorker(config_manager)
