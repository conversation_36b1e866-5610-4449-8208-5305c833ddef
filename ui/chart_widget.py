"""
图表展示组件模块

提供各种图表的展示功能
"""

import math
from typing import Dict, List, Any, Optional, Tuple
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QFrame, QSizePolicy
)
from PyQt6.QtGui import QPainter, QPen, QBrush, QColor, QFont, QPainterPath
from PyQt6.QtCore import Qt, QRect, QPoint, pyqtSignal

from .base_widget import BaseWidget
from core.data_models import EvaluationResult, ScoreInfo
from utils.constants import ErrorType
from utils.style_utils import StyleManager, get_error_color, get_score_color, FontManager
from utils.exception_handler import log_info, log_warning


class ScoreGaugeWidget(BaseWidget):
    """分数仪表盘组件"""
    
    def __init__(self, title: str = "总分", parent=None):
        self.title = title
        self.score = 0.0
        self.max_score = 100.0
        super().__init__(parent)
        self.setMinimumSize(200, 200)
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 标题
        self.title_label = QLabel(self.title)
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.title_label.setFont(FontManager.get_font("subtitle"))
        layout.addWidget(self.title_label)
        
        # 分数标签
        self.score_label = QLabel("0.0")
        self.score_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.score_label.setFont(FontManager.get_font("title", 24))
        layout.addWidget(self.score_label)
    
    def set_score(self, score: float, max_score: float = 100.0):
        """设置分数
        
        Args:
            score: 分数
            max_score: 最大分数
        """
        self.score = score
        self.max_score = max_score
        self.score_label.setText(f"{score:.1f}")
        
        # 设置颜色
        color = get_score_color(score)
        self.score_label.setStyleSheet(f"color: {color};")
        
        self.update()
    
    def paintEvent(self, event):
        """绘制事件"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 计算绘制区域
        rect = self.rect()
        center = rect.center()
        radius = min(rect.width(), rect.height()) // 2 - 20
        
        # 绘制背景圆环
        painter.setPen(QPen(QColor("#E0E0E0"), 8))
        painter.drawArc(center.x() - radius, center.y() - radius, 
                       radius * 2, radius * 2, 0, 360 * 16)
        
        # 绘制分数圆环
        if self.score > 0:
            angle = int((self.score / self.max_score) * 360 * 16)
            color = get_score_color(self.score)
            painter.setPen(QPen(QColor(color), 8))
            painter.drawArc(center.x() - radius, center.y() - radius,
                           radius * 2, radius * 2, 90 * 16, -angle)


class ErrorDistributionChart(BaseWidget):
    """错误分布图表"""
    
    def __init__(self, parent=None):
        self.error_data = {}
        super().__init__(parent)
        self.setMinimumSize(300, 200)
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("错误分布")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(FontManager.get_font("subtitle"))
        layout.addWidget(title_label)
        
        # 图例
        self.legend_layout = QHBoxLayout()
        layout.addLayout(self.legend_layout)
    
    def set_error_data(self, error_counts: Dict[ErrorType, int]):
        """设置错误数据
        
        Args:
            error_counts: 错误计数字典
        """
        self.error_data = error_counts
        self._update_legend()
        self.update()
    
    def _update_legend(self):
        """更新图例"""
        # 清除现有图例
        for i in reversed(range(self.legend_layout.count())):
            self.legend_layout.itemAt(i).widget().setParent(None)
        
        # 错误类型名称
        error_names = {
            ErrorType.INITIAL_CONSONANT: "声母",
            ErrorType.FINAL_SOUND: "韵母",
            ErrorType.TONE: "声调",
            ErrorType.FLUENCY: "流畅性",
            ErrorType.MISSING: "漏读"
        }
        
        for error_type, count in self.error_data.items():
            if count > 0:
                # 创建图例项
                legend_item = QFrame()
                legend_layout = QHBoxLayout(legend_item)
                legend_layout.setContentsMargins(5, 2, 5, 2)
                
                # 颜色块
                color_label = QLabel()
                color_label.setFixedSize(12, 12)
                color = get_error_color(error_type)
                color_label.setStyleSheet(f"background-color: {color}; border-radius: 6px;")
                legend_layout.addWidget(color_label)
                
                # 文本
                text_label = QLabel(f"{error_names.get(error_type, '未知')} ({count})")
                text_label.setFont(FontManager.get_font("small"))
                legend_layout.addWidget(text_label)
                
                self.legend_layout.addWidget(legend_item)
        
        self.legend_layout.addStretch()
    
    def paintEvent(self, event):
        """绘制事件"""
        super().paintEvent(event)
        
        if not self.error_data:
            return
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 计算总数
        total_errors = sum(self.error_data.values())
        if total_errors == 0:
            return
        
        # 计算绘制区域
        rect = self.rect()
        chart_rect = QRect(rect.x() + 20, rect.y() + 60, 
                          rect.width() - 40, rect.height() - 120)
        
        # 绘制柱状图
        bar_width = chart_rect.width() // len(self.error_data)
        max_count = max(self.error_data.values())
        
        x = chart_rect.x()
        for error_type, count in self.error_data.items():
            if count > 0:
                # 计算柱子高度
                bar_height = int((count / max_count) * chart_rect.height())
                
                # 绘制柱子
                color = get_error_color(error_type)
                painter.fillRect(x, chart_rect.bottom() - bar_height,
                               bar_width - 5, bar_height, QColor(color))
                
                # 绘制数值
                painter.setPen(QPen(QColor("#333333")))
                painter.setFont(FontManager.get_font("small"))
                painter.drawText(x, chart_rect.bottom() + 15, 
                               bar_width - 5, 20, 
                               Qt.AlignmentFlag.AlignCenter, str(count))
            
            x += bar_width


class ScoreTrendChart(BaseWidget):
    """分数趋势图表"""
    
    def __init__(self, parent=None):
        self.score_history = []
        super().__init__(parent)
        self.setMinimumSize(400, 200)
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("分数趋势")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(FontManager.get_font("subtitle"))
        layout.addWidget(title_label)
    
    def add_score(self, score: float, label: str = ""):
        """添加分数数据点
        
        Args:
            score: 分数
            label: 标签
        """
        self.score_history.append((score, label))
        self.update()
    
    def set_score_history(self, scores: List[Tuple[float, str]]):
        """设置分数历史
        
        Args:
            scores: 分数历史列表
        """
        self.score_history = scores
        self.update()
    
    def paintEvent(self, event):
        """绘制事件"""
        super().paintEvent(event)
        
        if len(self.score_history) < 2:
            return
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 计算绘制区域
        rect = self.rect()
        chart_rect = QRect(rect.x() + 30, rect.y() + 30,
                          rect.width() - 60, rect.height() - 60)
        
        # 计算数据范围
        scores = [score for score, _ in self.score_history]
        min_score = min(scores)
        max_score = max(scores)
        score_range = max_score - min_score
        
        if score_range == 0:
            score_range = 1
        
        # 绘制网格线
        painter.setPen(QPen(QColor("#E0E0E0"), 1))
        for i in range(5):
            y = chart_rect.y() + (chart_rect.height() * i // 4)
            painter.drawLine(chart_rect.x(), y, chart_rect.right(), y)
        
        # 绘制趋势线
        painter.setPen(QPen(QColor("#2196F3"), 2))
        
        points = []
        for i, (score, _) in enumerate(self.score_history):
            x = chart_rect.x() + (chart_rect.width() * i // (len(self.score_history) - 1))
            y = chart_rect.bottom() - int(((score - min_score) / score_range) * chart_rect.height())
            points.append(QPoint(x, y))
        
        # 绘制线条
        for i in range(len(points) - 1):
            painter.drawLine(points[i], points[i + 1])
        
        # 绘制数据点
        painter.setBrush(QBrush(QColor("#2196F3")))
        for point in points:
            painter.drawEllipse(point.x() - 3, point.y() - 3, 6, 6)


class StatisticsPanel(BaseWidget):
    """统计面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("统计信息")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(FontManager.get_font("subtitle"))
        layout.addWidget(title_label)
        
        # 统计项容器
        self.stats_layout = QVBoxLayout()
        layout.addLayout(self.stats_layout)
        
        layout.addStretch()
    
    def update_statistics(self, stats: Dict[str, Any]):
        """更新统计信息
        
        Args:
            stats: 统计数据
        """
        # 清除现有统计项
        for i in reversed(range(self.stats_layout.count())):
            self.stats_layout.itemAt(i).widget().setParent(None)
        
        # 添加统计项
        stat_items = [
            ("总分", f"{stats.get('total_score', 0):.1f}"),
            ("准确率", f"{stats.get('accuracy_rate', 0):.1%}"),
            ("总错误数", str(stats.get('total_errors', 0))),
            ("处理时间", f"{stats.get('processing_time', 0):.2f}s")
        ]
        
        for label, value in stat_items:
            self._add_stat_item(label, value)
    
    def _add_stat_item(self, label: str, value: str):
        """添加统计项
        
        Args:
            label: 标签
            value: 值
        """
        item_widget = QFrame()
        item_layout = QHBoxLayout(item_widget)
        item_layout.setContentsMargins(10, 5, 10, 5)
        
        # 标签
        label_widget = QLabel(label)
        label_widget.setFont(FontManager.get_font("content"))
        item_layout.addWidget(label_widget)
        
        item_layout.addStretch()
        
        # 值
        value_widget = QLabel(value)
        value_widget.setFont(FontManager.get_bold_font("content"))
        value_widget.setStyleSheet("color: #2196F3;")
        item_layout.addWidget(value_widget)
        
        self.stats_layout.addWidget(item_widget)


class ChartContainer(BaseWidget):
    """图表容器"""
    
    # 图表切换信号
    chart_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.charts = {}
        self.current_chart = None
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar = QHBoxLayout()
        
        # 图表切换按钮
        self.gauge_btn = QPushButton("仪表盘")
        self.distribution_btn = QPushButton("错误分布")
        self.trend_btn = QPushButton("趋势图")
        
        self.gauge_btn.clicked.connect(lambda: self.switch_chart("gauge"))
        self.distribution_btn.clicked.connect(lambda: self.switch_chart("distribution"))
        self.trend_btn.clicked.connect(lambda: self.switch_chart("trend"))
        
        toolbar.addWidget(self.gauge_btn)
        toolbar.addWidget(self.distribution_btn)
        toolbar.addWidget(self.trend_btn)
        toolbar.addStretch()
        
        layout.addLayout(toolbar)
        
        # 图表区域
        self.chart_area = QVBoxLayout()
        layout.addLayout(self.chart_area)
    
    def add_chart(self, name: str, chart_widget: QWidget):
        """添加图表
        
        Args:
            name: 图表名称
            chart_widget: 图表组件
        """
        self.charts[name] = chart_widget
        chart_widget.setVisible(False)
        self.chart_area.addWidget(chart_widget)
        
        if self.current_chart is None:
            self.switch_chart(name)
    
    def switch_chart(self, name: str):
        """切换图表
        
        Args:
            name: 图表名称
        """
        if name not in self.charts:
            return
        
        # 隐藏当前图表
        if self.current_chart and self.current_chart in self.charts:
            self.charts[self.current_chart].setVisible(False)
        
        # 显示新图表
        self.charts[name].setVisible(True)
        self.current_chart = name
        
        # 更新按钮状态
        self._update_button_states(name)
        
        # 发送信号
        self.chart_changed.emit(name)
    
    def _update_button_states(self, active_chart: str):
        """更新按钮状态
        
        Args:
            active_chart: 当前活动图表
        """
        buttons = {
            "gauge": self.gauge_btn,
            "distribution": self.distribution_btn,
            "trend": self.trend_btn
        }
        
        for chart_name, button in buttons.items():
            if chart_name == active_chart:
                button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; }")
            else:
                button.setStyleSheet("")
