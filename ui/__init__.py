"""
UI模块

包含所有用户界面相关的组件和窗口
"""

# 版本信息
__version__ = "1.0.0"
__author__ = "开发团队"

# 导出主要的UI组件
from .main_window import MainWindow
from .base_widget import (
    BaseWidget, SectionWidget, ToolBarWidget,
    StatusBarWidget, PanelWidget, PlaceholderWidget
)
from .upload_widget import (
    FileUploadWidget, DropZoneWidget, FileListWidget, TextInputWidget
)
from .result_widget import (
    ResultDisplayWidget, AnnotatedTextWidget, ErrorDetailWidget, create_result_display
)
from .chart_widget import (
    ScoreGaugeWidget, ErrorDistributionChart, StatisticsPanel,
    ChartContainer, ScoreTrendChart
)
from .settings_dialog import SettingsDialog, show_settings_dialog

__all__ = [
    "MainWindow",
    "BaseWidget",
    "SectionWidget",
    "ToolBarWidget",
    "StatusBarWidget",
    "PanelWidget",
    "PlaceholderWidget",
    "FileUploadWidget",
    "DropZoneWidget",
    "FileListWidget",
    "TextInputWidget",
    "ResultDisplayWidget",
    "AnnotatedTextWidget",
    "ErrorDetailWidget",
    "create_result_display",
    "ScoreGaugeWidget",
    "ErrorDistributionChart",
    "StatisticsPanel",
    "ChartContainer",
    "ScoreTrendChart",
    "SettingsDialog",
    "show_settings_dialog"
]
