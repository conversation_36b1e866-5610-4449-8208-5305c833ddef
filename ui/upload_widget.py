"""
文件上传组件模块

提供音频和文本文件的上传界面和功能
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QProgressBar, QFileDialog, QListWidget, QListWidgetItem,
    QMessageBox, QGroupBox, QSplitter, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QMimeData, QUrl, QThread
from PyQt6.QtGui import QDragEnterEvent, QDropEvent, QPalette, QFont

from pathlib import Path
from typing import List, Optional, Dict, Any

from .base_widget import BaseWidget, SectionWidget
from core.audio_processor import AudioProcessorWorker
from utils.validators import FileValidator, validate_text_content
from utils.file_utils import FileUtils, format_file_size
from utils.constants import SUPPORTED_AUDIO_FORMATS


class DropZoneWidget(QWidget):
    """拖拽上传区域组件"""
    
    # 文件拖拽信号
    files_dropped = pyqtSignal(list)  # 文件路径列表
    
    def __init__(self, accept_types: List[str] = None, parent=None):
        super().__init__(parent)
        self.accept_types = accept_types or SUPPORTED_AUDIO_FORMATS
        self.setup_ui()
        self.setup_style()
        
        # 启用拖拽
        self.setAcceptDrops(True)
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 拖拽提示图标（使用文本代替）
        self.icon_label = QLabel("📁")
        self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.icon_label.setStyleSheet("font-size: 48px;")
        layout.addWidget(self.icon_label)
        
        # 主要提示文本
        self.main_label = QLabel("拖拽文件到此处")
        self.main_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.main_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(self.main_label)
        
        # 次要提示文本
        formats_text = ", ".join(self.accept_types)
        self.sub_label = QLabel(f"支持格式: {formats_text}")
        self.sub_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.sub_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        layout.addWidget(self.sub_label)
        
        # 或者按钮
        self.or_label = QLabel("或者")
        self.or_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.or_label.setStyleSheet("color: #6c757d; margin: 10px;")
        layout.addWidget(self.or_label)
        
        # 选择文件按钮
        self.select_button = QPushButton("选择文件")
        self.select_button.clicked.connect(self.select_files)
        layout.addWidget(self.select_button)
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            DropZoneWidget {
                border: 2px dashed #dee2e6;
                border-radius: 8px;
                background-color: #f8f9fa;
                min-height: 200px;
            }
            DropZoneWidget:hover {
                border-color: #007bff;
                background-color: #e3f2fd;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查文件类型
            urls = event.mimeData().urls()
            valid_files = []
            
            for url in urls:
                if url.isLocalFile():
                    file_path = Path(url.toLocalFile())
                    if file_path.suffix.lower() in self.accept_types:
                        valid_files.append(file_path)
            
            if valid_files:
                event.acceptProposedAction()
                self.setStyleSheet(self.styleSheet() + """
                    DropZoneWidget {
                        border-color: #28a745;
                        background-color: #d4edda;
                    }
                """)
            else:
                event.ignore()
        else:
            event.ignore()
    
    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        self.setup_style()  # 恢复原始样式
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        urls = event.mimeData().urls()
        file_paths = []
        
        for url in urls:
            if url.isLocalFile():
                file_path = Path(url.toLocalFile())
                if file_path.suffix.lower() in self.accept_types:
                    file_paths.append(str(file_path))
        
        if file_paths:
            self.files_dropped.emit(file_paths)
        
        self.setup_style()  # 恢复原始样式
        event.acceptProposedAction()
    
    def select_files(self):
        """选择文件对话框"""
        # 构建文件过滤器
        filters = []
        if '.wav' in self.accept_types:
            filters.append("WAV文件 (*.wav)")
        if '.mp3' in self.accept_types:
            filters.append("MP3文件 (*.mp3)")
        if '.m4a' in self.accept_types:
            filters.append("M4A文件 (*.m4a)")
        if '.txt' in self.accept_types:
            filters.append("文本文件 (*.txt)")
        
        filters.append("所有支持的文件 (*" + " *".join(self.accept_types) + ")")
        filter_string = ";;".join(filters)
        
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "选择文件",
            "",
            filter_string
        )
        
        if file_paths:
            self.files_dropped.emit(file_paths)


class FileListWidget(QListWidget):
    """文件列表组件"""
    
    # 文件操作信号
    file_removed = pyqtSignal(str)  # 文件路径
    file_selected = pyqtSignal(str)  # 文件路径
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        self.setAlternatingRowColors(True)
        self.setSelectionMode(QListWidget.SelectionMode.SingleSelection)
        
        # 连接信号
        self.itemClicked.connect(self.on_item_clicked)
    
    def add_file(self, file_path: str, file_info: Dict[str, Any]):
        """添加文件到列表
        
        Args:
            file_path: 文件路径
            file_info: 文件信息
        """
        item = QListWidgetItem()
        
        # 设置显示文本
        name = file_info.get("name", Path(file_path).name)
        size = format_file_size(file_info.get("size", 0))
        display_text = f"{name} ({size})"
        
        item.setText(display_text)
        item.setData(Qt.ItemDataRole.UserRole, file_path)
        
        # 设置工具提示
        tooltip = f"文件: {name}\n路径: {file_path}\n大小: {size}"
        if "validation_error" in file_info:
            tooltip += f"\n错误: {file_info['validation_error']}"
            item.setBackground(QPalette().color(QPalette.ColorRole.Base))
        
        item.setToolTip(tooltip)
        
        self.addItem(item)
    
    def remove_selected_file(self):
        """移除选中的文件"""
        current_item = self.currentItem()
        if current_item:
            file_path = current_item.data(Qt.ItemDataRole.UserRole)
            row = self.row(current_item)
            self.takeItem(row)
            self.file_removed.emit(file_path)
    
    def clear_files(self):
        """清空文件列表"""
        self.clear()
    
    def get_all_files(self) -> List[str]:
        """获取所有文件路径"""
        files = []
        for i in range(self.count()):
            item = self.item(i)
            file_path = item.data(Qt.ItemDataRole.UserRole)
            files.append(file_path)
        return files
    
    def on_item_clicked(self, item: QListWidgetItem):
        """文件项点击事件"""
        file_path = item.data(Qt.ItemDataRole.UserRole)
        self.file_selected.emit(file_path)


class TextInputWidget(BaseWidget):
    """文本输入组件"""
    
    # 文本变更信号
    text_changed = pyqtSignal(str)
    text_validated = pyqtSignal(bool, str)  # 是否有效, 错误信息
    
    def __init__(self, parent=None):
        super().__init__(parent)
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 文本编辑器
        self.text_edit = QTextEdit()
        self.text_edit.setPlaceholderText("请输入或粘贴测试文本...")
        self.text_edit.setMaximumHeight(150)
        self.text_edit.textChanged.connect(self.on_text_changed)
        layout.addWidget(self.text_edit)
        
        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: #6c757d; font-size: 12px;")
        layout.addWidget(self.status_label)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        # 加载文本文件按钮
        self.load_button = QPushButton("加载文本文件")
        self.load_button.clicked.connect(self.load_text_file)
        button_layout.addWidget(self.load_button)
        
        # 清空按钮
        self.clear_button = QPushButton("清空")
        self.clear_button.clicked.connect(self.clear_text)
        button_layout.addWidget(self.clear_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def on_text_changed(self):
        """文本变更处理"""
        text = self.text_edit.toPlainText()
        self.text_changed.emit(text)
        
        # 验证文本
        if text.strip():
            is_valid, error = validate_text_content(text)
            if is_valid:
                self.status_label.setText(f"文本长度: {len(text.encode('utf-8'))} 字节")
                self.status_label.setStyleSheet("color: #28a745; font-size: 12px;")
            else:
                self.status_label.setText(f"错误: {error}")
                self.status_label.setStyleSheet("color: #dc3545; font-size: 12px;")
            
            self.text_validated.emit(is_valid, error)
        else:
            self.status_label.setText("")
    
    def load_text_file(self):
        """加载文本文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择文本文件",
            "",
            "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                self.text_edit.setPlainText(content)
                
            except Exception as e:
                QMessageBox.warning(self, "错误", f"加载文本文件失败:\n{str(e)}")
    
    def clear_text(self):
        """清空文本"""
        self.text_edit.clear()
    
    def get_text(self) -> str:
        """获取文本内容"""
        return self.text_edit.toPlainText()
    
    def set_text(self, text: str):
        """设置文本内容"""
        self.text_edit.setPlainText(text)


class FileUploadWidget(BaseWidget):
    """文件上传主组件"""
    
    # 文件上传信号
    files_uploaded = pyqtSignal(list)  # 文件信息列表
    audio_processed = pyqtSignal(dict)  # 音频处理结果
    text_ready = pyqtSignal(str)       # 文本内容就绪
    upload_progress = pyqtSignal(int)   # 上传进度
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.uploaded_files = []
        self.current_text = ""
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)
        
        # 音频文件上传区域
        audio_group = QGroupBox("音频文件")
        audio_layout = QVBoxLayout(audio_group)
        
        # 拖拽区域
        self.drop_zone = DropZoneWidget(SUPPORTED_AUDIO_FORMATS)
        self.drop_zone.files_dropped.connect(self.handle_files_dropped)
        audio_layout.addWidget(self.drop_zone)
        
        # 文件列表
        self.file_list = FileListWidget()
        self.file_list.file_removed.connect(self.remove_file)
        self.file_list.file_selected.connect(self.select_file)
        audio_layout.addWidget(self.file_list)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        audio_layout.addWidget(self.progress_bar)
        
        splitter.addWidget(audio_group)
        
        # 文本输入区域
        text_group = QGroupBox("测试文本")
        text_layout = QVBoxLayout(text_group)
        
        self.text_input = TextInputWidget()
        self.text_input.text_changed.connect(self.handle_text_changed)
        text_layout.addWidget(self.text_input)
        
        splitter.addWidget(text_group)
        
        # 设置分割器比例
        splitter.setSizes([300, 200])
    
    def handle_files_dropped(self, file_paths: List[str]):
        """处理拖拽的文件"""
        # 验证文件
        path_objects = [Path(path) for path in file_paths]
        validation_result = FileValidator.validate_batch_files(path_objects)
        
        valid_files = validation_result["valid"]
        invalid_files = validation_result["invalid"]
        
        # 显示无效文件的错误信息
        if invalid_files:
            error_messages = []
            for file_info in invalid_files:
                error_messages.append(f"{file_info['name']}: {file_info['validation_error']}")
            
            QMessageBox.warning(
                self,
                "文件验证失败",
                f"以下文件无法使用:\n\n" + "\n".join(error_messages)
            )
        
        # 添加有效文件到列表
        for file_info in valid_files:
            self.file_list.add_file(file_info["path"], file_info)
            self.uploaded_files.append(file_info)
        
        if valid_files:
            self.files_uploaded.emit(valid_files)
    
    def remove_file(self, file_path: str):
        """移除文件"""
        self.uploaded_files = [f for f in self.uploaded_files if f["path"] != file_path]
    
    def select_file(self, file_path: str):
        """选择文件进行处理"""
        if file_path.lower().endswith(tuple(SUPPORTED_AUDIO_FORMATS)):
            self.process_audio_file(file_path)
    
    def process_audio_file(self, file_path: str):
        """处理音频文件"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 创建音频处理工作线程
        self.audio_worker = AudioProcessorWorker(Path(file_path))
        self.audio_worker.progress_updated.connect(self.progress_bar.setValue)
        self.audio_worker.processing_finished.connect(self.on_audio_processed)
        self.audio_worker.start()
    
    def on_audio_processed(self, success: bool, message: str, result: Dict[str, Any]):
        """音频处理完成"""
        self.progress_bar.setVisible(False)
        
        if success:
            self.audio_processed.emit(result)
        else:
            QMessageBox.warning(self, "音频处理失败", message)
    
    def handle_text_changed(self, text: str):
        """处理文本变更"""
        self.current_text = text
        if text.strip():
            self.text_ready.emit(text)
    
    def get_uploaded_files(self) -> List[Dict[str, Any]]:
        """获取已上传的文件"""
        return self.uploaded_files
    
    def get_current_text(self) -> str:
        """获取当前文本"""
        return self.current_text
    
    def clear_all(self):
        """清空所有内容"""
        self.file_list.clear_files()
        self.text_input.clear_text()
        self.uploaded_files = []
        self.current_text = ""
