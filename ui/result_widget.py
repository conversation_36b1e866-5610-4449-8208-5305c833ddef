"""
结果展示组件模块

提供评测结果的展示界面
"""

from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QLabel,
    QPushButton, QSplitter, QScrollArea, QFrame, QMessageBox,
    QFileDialog, QTabWidget
)
from PyQt6.QtCore import Qt, pyqtSignal, QPoint
from PyQt6.QtGui import QFont, QTextCursor

from .base_widget import BaseWidget, SectionWidget
from .chart_widget import (
    ScoreGaugeWidget, ErrorDistributionChart, StatisticsPanel,
    ChartContainer, ScoreTrendChart
)
from core.annotation_engine import AnnotationEngine
from core.data_models import EvaluationResult
from utils.constants import ErrorType
from utils.style_utils import StyleSheets, FontManager
from utils.exception_handler import log_info, log_error


class AnnotatedTextWidget(BaseWidget):
    """标注文本组件"""
    
    # 错误点击信号
    error_clicked = pyqtSignal(object)  # ErrorInfo对象
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.annotation_engine = AnnotationEngine()
        self.evaluation_result = None
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar = QHBoxLayout()
        
        self.export_btn = QPushButton("导出")
        self.clear_btn = QPushButton("清除")
        
        self.export_btn.clicked.connect(self.export_annotations)
        self.clear_btn.clicked.connect(self.clear_annotations)
        
        toolbar.addWidget(self.export_btn)
        toolbar.addWidget(self.clear_btn)
        toolbar.addStretch()
        
        layout.addLayout(toolbar)
        
        # 文本编辑器
        self.text_edit = QTextEdit()
        self.text_edit.setReadOnly(True)
        self.text_edit.setFont(FontManager.get_font("content", 14))
        self.text_edit.setStyleSheet(StyleSheets.get_text_edit_style())
        
        # 连接鼠标事件
        self.text_edit.mousePressEvent = self._on_mouse_press
        self.text_edit.mouseMoveEvent = self._on_mouse_move
        
        layout.addWidget(self.text_edit)
    
    def set_evaluation_result(self, result: EvaluationResult):
        """设置评测结果
        
        Args:
            result: 评测结果
        """
        self.evaluation_result = result
        
        # 应用标注
        success = self.annotation_engine.annotate_text(self.text_edit, result)
        if success:
            log_info(f"文本标注成功: {result.request_id}")
        else:
            log_error(f"文本标注失败: {result.request_id}")
    
    def _on_mouse_press(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            cursor = self.text_edit.cursorForPosition(event.pos())
            position = cursor.position()
            
            # 获取错误信息
            error = self.annotation_engine.get_error_at_position(self.text_edit, position)
            if error:
                self.error_clicked.emit(error)
        
        # 调用原始事件处理
        QTextEdit.mousePressEvent(self.text_edit, event)
    
    def _on_mouse_move(self, event):
        """鼠标移动事件"""
        cursor = self.text_edit.cursorForPosition(event.pos())
        position = cursor.position()
        
        # 显示工具提示
        global_pos = self.text_edit.mapToGlobal(event.pos())
        self.annotation_engine.show_error_tooltip(self.text_edit, global_pos, position)
        
        # 调用原始事件处理
        QTextEdit.mouseMoveEvent(self.text_edit, event)
    
    def export_annotations(self):
        """导出标注"""
        if not self.evaluation_result:
            QMessageBox.warning(self, "警告", "没有可导出的结果")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出标注结果", 
            f"evaluation_result_{self.evaluation_result.request_id}.html",
            "HTML文件 (*.html);;文本文件 (*.txt)"
        )
        
        if file_path:
            try:
                if file_path.endswith('.html'):
                    content = self.annotation_engine.export_annotations(self.text_edit, "html")
                else:
                    content = self.annotation_engine.export_annotations(self.text_edit, "text")
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                QMessageBox.information(self, "成功", f"结果已导出到: {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
    
    def clear_annotations(self):
        """清除标注"""
        self.text_edit.clear()
        if self.evaluation_result:
            self.annotation_engine.clear_annotation_data(self.text_edit)
        self.evaluation_result = None


class ErrorDetailWidget(BaseWidget):
    """错误详情组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_error = None
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        self.title_label = QLabel("错误详情")
        self.title_label.setFont(FontManager.get_font("subtitle"))
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.title_label)
        
        # 详情区域
        self.detail_area = QScrollArea()
        self.detail_widget = QWidget()
        self.detail_layout = QVBoxLayout(self.detail_widget)
        
        self.detail_area.setWidget(self.detail_widget)
        self.detail_area.setWidgetResizable(True)
        layout.addWidget(self.detail_area)
        
        # 默认显示
        self._show_no_error()
    
    def show_error_detail(self, error_info):
        """显示错误详情
        
        Args:
            error_info: 错误信息对象
        """
        self.current_error = error_info
        
        # 清除现有内容
        for i in reversed(range(self.detail_layout.count())):
            self.detail_layout.itemAt(i).widget().setParent(None)
        
        # 错误类型
        self._add_detail_item("错误类型", self._get_error_type_name(error_info.error_type))
        
        # 严重程度
        self._add_detail_item("严重程度", self._get_severity_name(error_info.severity))
        
        # 位置信息
        pos_text = f"字节: {error_info.position.begin_pos}-{error_info.position.end_pos}"
        if error_info.position.char_begin is not None:
            pos_text += f", 字符: {error_info.position.char_begin}-{error_info.position.char_end}"
        self._add_detail_item("位置", pos_text)
        
        # 分数
        if error_info.score is not None:
            self._add_detail_item("得分", f"{error_info.score:.1f}")
        
        # 期望内容
        if error_info.expected_content:
            self._add_detail_item("期望发音", error_info.expected_content)
        
        # 实际内容
        if error_info.actual_content:
            self._add_detail_item("实际发音", error_info.actual_content)
        
        # 建议
        if error_info.suggestion:
            self._add_detail_item("改进建议", error_info.suggestion)
        
        # 置信度
        if error_info.confidence is not None:
            self._add_detail_item("置信度", f"{error_info.confidence:.1%}")
        
        self.detail_layout.addStretch()
    
    def _show_no_error(self):
        """显示无错误状态"""
        # 清除现有内容
        for i in reversed(range(self.detail_layout.count())):
            self.detail_layout.itemAt(i).widget().setParent(None)
        
        no_error_label = QLabel("点击文本中的错误位置查看详情")
        no_error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        no_error_label.setStyleSheet("color: #666666; font-style: italic;")
        self.detail_layout.addWidget(no_error_label)
        
        self.detail_layout.addStretch()
    
    def _add_detail_item(self, label: str, value: str):
        """添加详情项
        
        Args:
            label: 标签
            value: 值
        """
        item_frame = QFrame()
        item_layout = QVBoxLayout(item_frame)
        item_layout.setContentsMargins(10, 5, 10, 5)
        
        # 标签
        label_widget = QLabel(label)
        label_widget.setFont(FontManager.get_font("small"))
        label_widget.setStyleSheet("color: #666666; font-weight: bold;")
        item_layout.addWidget(label_widget)
        
        # 值
        value_widget = QLabel(value)
        value_widget.setFont(FontManager.get_font("content"))
        value_widget.setWordWrap(True)
        item_layout.addWidget(value_widget)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        item_layout.addWidget(separator)
        
        self.detail_layout.addWidget(item_frame)
    
    def _get_error_type_name(self, error_type: ErrorType) -> str:
        """获取错误类型名称"""
        names = {
            ErrorType.INITIAL_CONSONANT: "声母错误",
            ErrorType.FINAL_SOUND: "韵母错误",
            ErrorType.TONE: "声调错误", 
            ErrorType.FLUENCY: "流畅性问题",
            ErrorType.MISSING: "漏读"
        }
        return names.get(error_type, "未知错误")
    
    def _get_severity_name(self, severity) -> str:
        """获取严重程度名称"""
        names = {
            "critical": "严重",
            "major": "主要",
            "minor": "轻微",
            "warning": "警告"
        }
        return names.get(severity.value, "未知")


class ResultDisplayWidget(BaseWidget):
    """结果展示主组件"""
    
    # 结果变更信号
    result_changed = pyqtSignal(object)  # EvaluationResult对象
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_result = None
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 文本标注选项卡
        self.annotation_tab = self._create_annotation_tab()
        self.tab_widget.addTab(self.annotation_tab, "文本标注")
        
        # 图表分析选项卡
        self.chart_tab = self._create_chart_tab()
        self.tab_widget.addTab(self.chart_tab, "图表分析")
        
        # 详细报告选项卡
        self.report_tab = self._create_report_tab()
        self.tab_widget.addTab(self.report_tab, "详细报告")
    
    def _create_annotation_tab(self) -> QWidget:
        """创建文本标注选项卡"""
        tab = QWidget()
        layout = QHBoxLayout(tab)
        
        # 主分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # 标注文本区域
        self.annotated_text = AnnotatedTextWidget()
        self.annotated_text.error_clicked.connect(self._on_error_clicked)
        splitter.addWidget(self.annotated_text)
        
        # 错误详情区域
        self.error_detail = ErrorDetailWidget()
        splitter.addWidget(self.error_detail)
        
        # 设置分割比例
        splitter.setSizes([600, 300])
        
        return tab
    
    def _create_chart_tab(self) -> QWidget:
        """创建图表分析选项卡"""
        tab = QWidget()
        layout = QHBoxLayout(tab)
        
        # 左侧图表区域
        chart_area = QVBoxLayout()
        
        # 分数仪表盘
        self.score_gauge = ScoreGaugeWidget("总分")
        chart_area.addWidget(self.score_gauge)
        
        # 图表容器
        self.chart_container = ChartContainer()
        
        # 添加各种图表
        self.error_distribution = ErrorDistributionChart()
        self.chart_container.add_chart("distribution", self.error_distribution)
        
        chart_area.addWidget(self.chart_container)
        
        layout.addLayout(chart_area)
        
        # 右侧统计面板
        self.statistics_panel = StatisticsPanel()
        layout.addWidget(self.statistics_panel)
        
        return tab
    
    def _create_report_tab(self) -> QWidget:
        """创建详细报告选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 工具栏
        toolbar = QHBoxLayout()
        
        export_report_btn = QPushButton("导出报告")
        print_btn = QPushButton("打印")
        
        export_report_btn.clicked.connect(self.export_report)
        print_btn.clicked.connect(self.print_report)
        
        toolbar.addWidget(export_report_btn)
        toolbar.addWidget(print_btn)
        toolbar.addStretch()
        
        layout.addLayout(toolbar)
        
        # 报告内容
        self.report_text = QTextEdit()
        self.report_text.setReadOnly(True)
        self.report_text.setFont(FontManager.get_font("content"))
        layout.addWidget(self.report_text)
        
        return tab
    
    def set_evaluation_result(self, result: EvaluationResult):
        """设置评测结果
        
        Args:
            result: 评测结果
        """
        self.current_result = result
        
        # 更新各个组件
        self._update_annotation_tab(result)
        self._update_chart_tab(result)
        self._update_report_tab(result)
        
        # 发送信号
        self.result_changed.emit(result)
        
        log_info(f"结果展示已更新: {result.request_id}")
    
    def _update_annotation_tab(self, result: EvaluationResult):
        """更新文本标注选项卡"""
        self.annotated_text.set_evaluation_result(result)
    
    def _update_chart_tab(self, result: EvaluationResult):
        """更新图表分析选项卡"""
        # 更新分数仪表盘
        self.score_gauge.set_score(result.scores.total_score)
        
        # 更新错误分布图
        error_counts = {}
        for error_type in ErrorType:
            count = result.get_error_count_by_type(error_type)
            if count > 0:
                error_counts[error_type] = count
        
        self.error_distribution.set_error_data(error_counts)
        
        # 更新统计面板
        stats = result.get_summary_stats()
        self.statistics_panel.update_statistics(stats)
    
    def _update_report_tab(self, result: EvaluationResult):
        """更新详细报告选项卡"""
        report_html = self._generate_report_html(result)
        self.report_text.setHtml(report_html)
    
    def _generate_report_html(self, result: EvaluationResult) -> str:
        """生成报告HTML
        
        Args:
            result: 评测结果
            
        Returns:
            HTML报告内容
        """
        html_parts = []
        
        # 标题
        html_parts.append(f"<h1>普通话测试评测报告</h1>")
        html_parts.append(f"<p><strong>请求ID:</strong> {result.request_id}</p>")
        html_parts.append(f"<p><strong>测试类型:</strong> {result.test_type.value}</p>")
        html_parts.append(f"<p><strong>测试时间:</strong> {result.created_at.strftime('%Y-%m-%d %H:%M:%S')}</p>")
        
        # 评分信息
        html_parts.append("<h2>评分结果</h2>")
        html_parts.append(f"<p><strong>总分:</strong> {result.scores.total_score:.1f}</p>")
        
        if result.scores.phone_score is not None:
            html_parts.append(f"<p><strong>声韵分:</strong> {result.scores.phone_score:.1f}</p>")
        
        if result.scores.tone_score is not None:
            html_parts.append(f"<p><strong>调型分:</strong> {result.scores.tone_score:.1f}</p>")
        
        if result.scores.fluency_score is not None:
            html_parts.append(f"<p><strong>流畅度分:</strong> {result.scores.fluency_score:.1f}</p>")
        
        # 错误统计
        html_parts.append("<h2>错误统计</h2>")
        stats = result.get_summary_stats()
        
        html_parts.append(f"<p><strong>总错误数:</strong> {stats['total_errors']}</p>")
        html_parts.append(f"<p><strong>准确率:</strong> {stats['accuracy_rate']:.1%}</p>")
        
        return "".join(html_parts)
    
    def _on_error_clicked(self, error_info):
        """错误点击处理
        
        Args:
            error_info: 错误信息
        """
        self.error_detail.show_error_detail(error_info)
    
    def export_report(self):
        """导出报告"""
        if not self.current_result:
            QMessageBox.warning(self, "警告", "没有可导出的报告")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出评测报告",
            f"evaluation_report_{self.current_result.request_id}.html",
            "HTML文件 (*.html);;PDF文件 (*.pdf)"
        )
        
        if file_path:
            try:
                content = self.report_text.toHtml()
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                QMessageBox.information(self, "成功", f"报告已导出到: {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
    
    def print_report(self):
        """打印报告"""
        if not self.current_result:
            QMessageBox.warning(self, "警告", "没有可打印的报告")
            return
        
        # 这里可以实现打印功能
        QMessageBox.information(self, "提示", "打印功能待实现")
    
    def clear_result(self):
        """清除结果"""
        self.current_result = None
        self.annotated_text.clear_annotations()
        self.error_detail._show_no_error()
        self.report_text.clear()
        self.score_gauge.set_score(0)
        self.error_distribution.set_error_data({})
        self.statistics_panel.update_statistics({})


# 便捷函数
def create_result_display() -> ResultDisplayWidget:
    """创建结果展示组件的便捷函数"""
    return ResultDisplayWidget()
