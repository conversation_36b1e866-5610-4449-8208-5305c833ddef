"""
基础UI组件模块

提供应用程序中使用的基础UI组件和工具类
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPalette


class BaseWidget(QWidget):
    """基础组件类
    
    提供通用的组件功能和样式设置
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_style()
    
    def setup_ui(self):
        """设置UI布局 - 子类重写"""
        pass
    
    def setup_style(self):
        """设置样式 - 子类重写"""
        pass
    
    def set_background_color(self, color: str):
        """设置背景颜色"""
        self.setStyleSheet(f"background-color: {color};")
    
    def set_border(self, width: int = 1, color: str = "#cccccc", style: str = "solid"):
        """设置边框"""
        self.setStyleSheet(f"border: {width}px {style} {color};")


class SectionWidget(BaseWidget):
    """区域组件类
    
    用于创建应用程序的各个功能区域
    """
    
    def __init__(self, title: str = "", parent=None):
        self.title = title
        super().__init__(parent)
    
    def setup_ui(self):
        """设置UI布局"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(10, 10, 10, 10)
        self.layout.setSpacing(10)
        
        # 标题
        if self.title:
            self.title_label = QLabel(self.title)
            self.title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
            self.title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
            self.layout.addWidget(self.title_label)
            
            # 分隔线
            separator = QFrame()
            separator.setFrameShape(QFrame.Shape.HLine)
            separator.setFrameShadow(QFrame.Shadow.Sunken)
            self.layout.addWidget(separator)
        
        # 内容区域
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.layout.addWidget(self.content_widget)
    
    def add_widget(self, widget: QWidget):
        """添加组件到内容区域"""
        self.content_layout.addWidget(widget)
    
    def add_layout(self, layout):
        """添加布局到内容区域"""
        self.content_layout.addLayout(layout)
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            SectionWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)


class ToolBarWidget(BaseWidget):
    """工具栏组件类"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.buttons = {}
    
    def setup_ui(self):
        """设置UI布局"""
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(10, 5, 10, 5)
        self.layout.setSpacing(10)
        
        # 左侧按钮区域
        self.left_layout = QHBoxLayout()
        self.layout.addLayout(self.left_layout)
        
        # 弹性空间
        self.layout.addStretch()
        
        # 右侧按钮区域
        self.right_layout = QHBoxLayout()
        self.layout.addLayout(self.right_layout)
    
    def add_button(self, name: str, text: str, position: str = "left") -> QPushButton:
        """添加按钮
        
        Args:
            name: 按钮名称
            text: 按钮文本
            position: 位置 ("left" 或 "right")
            
        Returns:
            创建的按钮对象
        """
        button = QPushButton(text)
        button.setObjectName(name)
        self.buttons[name] = button
        
        if position == "left":
            self.left_layout.addWidget(button)
        else:
            self.right_layout.addWidget(button)
        
        return button
    
    def get_button(self, name: str) -> QPushButton:
        """获取按钮"""
        return self.buttons.get(name)
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            ToolBarWidget {
                background-color: #ffffff;
                border-bottom: 1px solid #dee2e6;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)


class StatusBarWidget(BaseWidget):
    """状态栏组件类"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.status_label = None
        self.progress_label = None
    
    def setup_ui(self):
        """设置UI布局"""
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(10, 5, 10, 5)
        self.layout.setSpacing(10)
        
        # 状态文本
        self.status_label = QLabel("就绪")
        self.layout.addWidget(self.status_label)
        
        # 弹性空间
        self.layout.addStretch()
        
        # 进度信息
        self.progress_label = QLabel("")
        self.layout.addWidget(self.progress_label)
    
    def set_status(self, status: str):
        """设置状态文本"""
        if self.status_label:
            self.status_label.setText(status)
    
    def set_progress(self, progress: str):
        """设置进度信息"""
        if self.progress_label:
            self.progress_label.setText(progress)
    
    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            StatusBarWidget {
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
            }
            QLabel {
                color: #495057;
                font-size: 12px;
            }
        """)


class PanelWidget(SectionWidget):
    """面板组件类
    
    用于创建左侧、右侧等功能面板
    """
    
    # 面板切换信号
    panel_switched = pyqtSignal(str)  # 面板名称
    
    def __init__(self, title: str = "", collapsible: bool = False, parent=None):
        self.collapsible = collapsible
        self.collapsed = False
        super().__init__(title, parent)
    
    def setup_ui(self):
        """设置UI布局"""
        super().setup_ui()
        
        if self.collapsible and self.title:
            # 使标题可点击
            self.title_label.mousePressEvent = self.toggle_collapse
            self.title_label.setStyleSheet("QLabel:hover { color: #007bff; cursor: pointer; }")
    
    def toggle_collapse(self, event=None):
        """切换折叠状态"""
        if not self.collapsible:
            return
        
        self.collapsed = not self.collapsed
        self.content_widget.setVisible(not self.collapsed)
        
        # 更新标题显示
        if self.collapsed:
            self.title_label.setText(f"▶ {self.title}")
        else:
            self.title_label.setText(f"▼ {self.title}")
    
    def set_collapsed(self, collapsed: bool):
        """设置折叠状态"""
        if self.collapsed != collapsed:
            self.toggle_collapse()


class PlaceholderWidget(BaseWidget):
    """占位符组件类
    
    用于显示临时内容或空状态
    """
    
    def __init__(self, text: str = "此功能正在开发中...", parent=None):
        self.text = text
        super().__init__(parent)
    
    def setup_ui(self):
        """设置UI布局"""
        self.layout = QVBoxLayout(self)
        self.layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.label = QLabel(self.text)
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 14px;
                font-style: italic;
            }
        """)
        
        self.layout.addWidget(self.label)
    
    def set_text(self, text: str):
        """设置占位符文本"""
        self.text = text
        if hasattr(self, 'label'):
            self.label.setText(text)
