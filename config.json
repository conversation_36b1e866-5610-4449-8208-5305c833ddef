{"app": {"name": "普通话测试软件", "version": "1.0.0", "author": "开发团队", "description": "基于AI语音评测技术的普通话测试软件", "first_run": true}, "api": {"base_url": "https://api.xfyun.cn/v1/service/v1/ise", "app_id": "c94ba137", "api_key": "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", "api_secret": "Z0FBQUFBQm9qd2J6RFZNZUUxYzhYRDBlN1doMUNPdGhRaUJPQ3VJR0NjWkRaaUFtNlRPanMwNldONjA1ZzNvLWlsXzdmOGFMdVR4Wkdya1BoMHR3V1FsNV9qaXR4YVZIc2l4b1Yta1JpM0JWbVZ0VllsNjJIWjNxcUtCZHpNSVI2TGlBV09Yb0pmOUZUY211em90VXY0MEFpcC01UmpZRFd2RWotR0xMejNDUDhPNnNyQVpxTWttRjhjWUV2V3RhOHRJWkFXRFpuT29nVUdMZlh0eXhyd25oWHlWOGplcU5wZ0RrVXBWTGVRMDRuVUQ1MzVfeEI3OGU1SVU1eVhLVm8xZ0dVOVdyT29hdGJTQ3hVbFVpRVg0cnN4emk3bXh2UHV3SkYzbUpTUkxpNUltS3lwYm1RdkJpcnhqeERJbnMyRFdteWZQeHlCVUE=", "timeout": 30, "retry_count": 3}, "audio": {"supported_formats": ["wav", "mp3", "m4a", "pcm"], "max_file_size_mb": 5, "target_sample_rate": 16000, "target_channels": 1, "target_bit_depth": 16}, "ui": {"window": {"width": 1200, "height": 800, "min_width": 800, "min_height": 600, "x": 386, "y": 62, "maximized": false}, "theme": "light", "font_size": "medium", "language": "zh_CN", "show_tooltips": true, "auto_save": true}, "evaluation": {"language": "zh_cn", "mode": "standard", "strictness": "standard", "enable_multi_dimension": true, "auto_start": false}, "file": {"work_directory": "", "save_history": true, "audio_quality": "standard", "export_format": "json", "max_history_count": 100}, "annotation": {"show_initial_consonant": true, "show_final_sound": true, "show_tone": true, "show_fluency": true, "show_missing": true, "animation_enabled": true}}